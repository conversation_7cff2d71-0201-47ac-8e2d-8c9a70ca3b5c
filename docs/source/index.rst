

.. image:: _static/audtecheq_logo.png
   :align: center
   :width: 400px

.. centered:: audtecheq: A small python package to develop and maintain code for the AudioTechEquity project.


.. image:: https://github.com/PedzSTARlab/audtecheq/actions/workflows/docs.yml/badge.svg
        :target: https://github.com/PedzSTARlab/audtecheq/actions/workflows/docs.yml
 
.. image:: https://img.shields.io/pypi/v/audtecheq.svg
        :target: https://pypi.python.org/pypi/audtecheq

.. image:: https://img.shields.io/docker/pulls/PedzSTARlab/audtecheq
    :alt: Dockerpulls
    :target: https://cloud.docker.com/u/PedzSTARlab/repository/docker/PedzSTARlab/audtecheq

.. image:: https://img.shields.io/github/repo-size/PedzSTARlab/audtecheq.svg
        :target: https://github.com/PedzSTARlab/audtecheq.zip

.. image:: https://img.shields.io/github/issues/PedzSTARlab/audtecheq.svg
        :target: https://github.com/PedzSTARlab/audtecheq/issues

.. image:: https://img.shields.io/github/issues-pr/PedzSTARlab/audtecheq.svg
        :target: https://github.com/PedzSTARlab/audtecheq/pulls

.. image:: https://img.shields.io/github/license/PedzSTARlab/audtecheq.svg
        :target: https://github.com/PedzSTARlab/audtecheq




|


Introduction
============

``audtecheq`` aims to provide a set of tools and workflows for ... 

This documentation showcases the respective functionality and provides details concerning
its application and modules.

If you still have questions after going through provided here you can refer to 
the :ref:`api_ref` or ask a question on `GitHub <https://github.com/PedzSTARlab/audtecheq/issues>`_.


Contents
========
.. toctree::
   :maxdepth: 1

   installation
   usage
   walkthrough
   auto_examples/index
   api_ref
   release-history
   min_versions
