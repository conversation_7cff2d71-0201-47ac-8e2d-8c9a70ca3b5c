#!/usr/bin/env python3
"""
Modular script to create a CSV file that divides participants into at-risk groups
based on the at_risk_decision column from participants.tsv
"""

import pandas as pd
import json
import os
from pathlib import Path


def load_json_mappings(json_file_path):
    """
    Load field mappings from the participants.json file.
    
    Args:
        json_file_path (str): Path to the participants.json file
        
    Returns:
        dict: Dictionary containing all field mappings
    """
    try:
        with open(json_file_path, 'r') as f:
            mappings = json.load(f)
        return mappings
    except FileNotFoundError:
        print(f"Error: JSON file {json_file_path} not found!")
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file: {e}")
        return None


def extract_level_mappings(field_data):
    """
    Extract level mappings from a field's data structure.
    
    Args:
        field_data (dict): Field data from JSON containing 'Levels' key
        
    Returns:
        dict: Mapping of numeric codes to string descriptions
    """
    if 'Levels' not in field_data:
        return {}
    
    # Convert string keys to float for consistent mapping
    return {float(k): v for k, v in field_data['Levels'].items()}


def load_participant_data(tsv_file_path):
    """
    Load participant data from TSV file.
    
    Args:
        tsv_file_path (str): Path to the participants.tsv file
        
    Returns:
        pandas.DataFrame: Loaded participant data
    """
    try:
        df = pd.read_csv(tsv_file_path, sep='\t')
        print(f"Loaded {len(df)} participants from {tsv_file_path}")
        return df
    except FileNotFoundError:
        print(f"Error: TSV file {tsv_file_path} not found!")
        return None
    except Exception as e:
        print(f"Error loading TSV file: {e}")
        return None


def map_field_value(value, mapping_dict, default="N/A"):
    """
    Map a field value using the provided mapping dictionary.
    
    Args:
        value: The value to map
        mapping_dict (dict): Mapping dictionary
        default (str): Default value if mapping not found
        
    Returns:
        str: Mapped value or default
    """
    if pd.isna(value):
        return default
    return mapping_dict.get(float(value), default)


def process_participants(df, mappings):
    """
    Process participant data and create at-risk groups.
    
    Args:
        df (pandas.DataFrame): Participant data
        mappings (dict): Field mappings from JSON
        
    Returns:
        pandas.DataFrame: Processed data with at-risk groups
    """
    # Extract mappings for each field
    at_risk_mappings = extract_level_mappings(mappings.get('at_risk_decision', {}))
    sex_mappings = extract_level_mappings(mappings.get('ss_child_biosex', {}))
    hisp_mappings = extract_level_mappings(mappings.get('ss_child_hisp_latx', {}))
    
    result_data = []
    
    for _, row in df.iterrows():
        participant_id = row['id_number']
        at_risk_code = row['at_risk_decision']
        
        # Handle missing at_risk_decision values
        if pd.isna(at_risk_code):
            at_risk_group = "Missing data"
            at_risk_code_clean = "N/A"
        else:
            at_risk_group = at_risk_mappings.get(float(at_risk_code), "Unknown")
            at_risk_code_clean = int(at_risk_code)
        
        # Process other fields
        age_months = row['ss_child_age'] if not pd.isna(row['ss_child_age']) else "N/A"
        chronological_age = row['ss_child_chronological_age'] if not pd.isna(row['ss_child_chronological_age']) else "N/A"
        sex = map_field_value(row['ss_child_biosex'], sex_mappings)
        income_status = row['ss_demographic_groups'] if not pd.isna(row['ss_demographic_groups']) else "N/A"
        hispanic_latinx = map_field_value(row['ss_child_hisp_latx'], hisp_mappings)
        
        result_data.append({
            'participant_id': participant_id,
            'at_risk_group': at_risk_group,
            'at_risk_code': at_risk_code_clean,
            'age_months': age_months,
            'chronological_age': chronological_age,
            'sex': sex,
            'income_status': income_status,
            'hispanic_latinx': hispanic_latinx
        })
    
    return pd.DataFrame(result_data)


def save_results(df, output_file_path):
    """
    Save results to CSV file and print summary.
    
    Args:
        df (pandas.DataFrame): Processed data
        output_file_path (str): Path for output CSV file
    """
    # Sort by at_risk_code and then by participant_id
    df_sorted = df.sort_values(['at_risk_code', 'participant_id'])
    
    # Save to CSV
    df_sorted.to_csv(output_file_path, index=False)
    print(f"\nCreated {output_file_path} with {len(df_sorted)} participants")
    
    # Print summary statistics
    print("\n=== AT-RISK GROUP SUMMARY ===")
    group_counts = df_sorted['at_risk_group'].value_counts()
    for group, count in group_counts.items():
        print(f"{group}: {count} participants")
    
    # Print first few rows as preview
    print(f"\n=== PREVIEW OF {output_file_path} ===")
    print(df_sorted.head(10).to_string(index=False))


def main():
    """
    Main function to orchestrate the at-risk group analysis.
    """
    # Get the directory where this script is located
    script_dir = Path(__file__).parent
    
    # Define file paths relative to script location
    tsv_file = script_dir / "participants.tsv"
    json_file = script_dir / "participants.json"
    output_file = script_dir / "participants_at_risk_groups.csv"
    
    # Load mappings from JSON
    mappings = load_json_mappings(json_file)
    if mappings is None:
        return None
    
    # Load participant data
    df = load_participant_data(tsv_file)
    if df is None:
        return None
    
    # Process participants
    result_df = process_participants(df, mappings)
    
    # Save results
    save_results(result_df, output_file)
    
    return result_df


if __name__ == "__main__":
    df = main()
