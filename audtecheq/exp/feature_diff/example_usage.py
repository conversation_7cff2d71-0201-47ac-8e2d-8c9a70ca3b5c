#!/usr/bin/env python3
"""
Example usage of the Audio At-Risk Classification Pipeline.
This script demonstrates different ways to use the pipeline components.
"""

from pathlib import Path
import numpy as np

# Import pipeline components
from main_pipeline import AudioAtRiskPipeline
from config import ConfigManager
from data_loader import ParticipantDataLoader


def example_basic_usage():
    """Example of basic pipeline usage."""
    print("=== BASIC USAGE EXAMPLE ===")
    
    # Initialize pipeline with current directory
    pipeline = AudioAtRiskPipeline(base_dir=Path(__file__).parent)
    
    # Run the complete pipeline
    # Note: This will fail without audio files and required dependencies
    try:
        results = pipeline.run_full_pipeline(
            force_recompute_embeddings=False,
            compare_models=True
        )
        print("Pipeline completed successfully!")
        print(f"Results: {results}")
    except Exception as e:
        print(f"Pipeline failed (expected without audio files): {e}")


def example_step_by_step():
    """Example of step-by-step pipeline usage."""
    print("\n=== STEP-BY-STEP USAGE EXAMPLE ===")
    
    pipeline = AudioAtRiskPipeline(base_dir=Path(__file__).parent)
    
    try:
        # Step 1: Load participant data
        print("Step 1: Loading participant data...")
        participant_labels = pipeline.load_participant_data()
        print(f"Loaded {len(participant_labels)} participants with labels")
        
        # Step 2: Discover audio files
        print("Step 2: Discovering audio files...")
        audio_files = pipeline.discover_audio_files()
        print(f"Found audio files for {len(audio_files)} participants")
        
        if audio_files:
            # Step 3: Extract embeddings (would fail without dependencies)
            print("Step 3: Extracting embeddings...")
            embeddings = pipeline.extract_embeddings(audio_files)
            print(f"Extracted embeddings for {len(embeddings)} participants")
            
            # Step 4: Train and evaluate
            print("Step 4: Training classifier...")
            results = pipeline.train_and_evaluate(embeddings, participant_labels)
            print("Training completed!")
        else:
            print("No audio files found - skipping embedding extraction and training")
            
    except Exception as e:
        print(f"Step failed: {e}")


def example_data_analysis():
    """Example of using just the data loading components."""
    print("\n=== DATA ANALYSIS EXAMPLE ===")
    
    # Initialize configuration
    config = ConfigManager()
    config.set_base_directory(Path(__file__).parent)
    
    # Load and analyze participant data
    data_loader = ParticipantDataLoader(config)
    
    # Load metadata
    df = data_loader.load_participant_metadata()
    print(f"Total participants: {len(df)}")
    
    # Create at-risk groups
    at_risk_df = data_loader.create_at_risk_groups()
    
    # Analyze label distribution
    label_dist = data_loader.get_label_distribution()
    print("\nAt-risk group distribution:")
    for group, count in label_dist.items():
        percentage = (count / len(at_risk_df)) * 100
        print(f"  {group}: {count} ({percentage:.1f}%)")
    
    # Get classification data (excluding missing labels)
    participant_ids, labels = data_loader.get_classification_data(exclude_missing=True)
    print(f"\nParticipants with valid labels: {len(participant_ids)}")
    
    # Analyze by numeric labels
    unique_labels, counts = np.unique(labels, return_counts=True)
    print("\nNumeric label distribution:")
    for label, count in zip(unique_labels, counts):
        print(f"  Label {label}: {count} participants")


def example_custom_configuration():
    """Example of using custom configuration."""
    print("\n=== CUSTOM CONFIGURATION EXAMPLE ===")
    
    # Create custom configuration
    config = ConfigManager()
    
    # Modify audio settings
    config.audio_config.sample_rate = 22050  # Higher sample rate
    config.audio_config.apply_vad = False    # Disable VAD
    config.audio_config.embedding_model = "facebook/wav2vec2-large-960h"  # Larger model
    
    # Modify model settings
    config.model_config.classifier_type = "random_forest"
    config.model_config.test_size = 0.3
    config.model_config.cross_validation_folds = 10
    
    # Set base directory
    config.set_base_directory(Path(__file__).parent)
    
    # Save custom configuration
    config_file = Path(__file__).parent / "custom_config.json"
    config.save_to_file(config_file)
    print(f"Saved custom configuration to {config_file}")
    
    # Use custom configuration in pipeline
    pipeline = AudioAtRiskPipeline(
        base_dir=Path(__file__).parent,
        config_file=config_file
    )
    print("Pipeline initialized with custom configuration")


def example_model_comparison():
    """Example of comparing different models."""
    print("\n=== MODEL COMPARISON EXAMPLE ===")
    
    # This would work with actual embeddings and labels
    print("Model comparison requires:")
    print("1. Extracted audio embeddings")
    print("2. Participant labels")
    print("3. Installed ML libraries (scikit-learn, xgboost)")
    
    # Pseudo-code for model comparison:
    """
    pipeline = AudioAtRiskPipeline(base_dir=Path(__file__).parent)
    
    # Get embeddings and labels
    embeddings = {...}  # participant_id -> embedding
    labels = {...}      # participant_id -> label
    
    # Compare models
    comparison_results = pipeline.compare_models(embeddings, labels)
    
    # Results will show performance of different algorithms:
    # - Random Forest
    # - Logistic Regression  
    # - XGBoost (if available)
    """


def show_expected_directory_structure():
    """Show the expected directory structure."""
    print("\n=== EXPECTED DIRECTORY STRUCTURE ===")
    
    structure = """
    audtecheq/exp/feature_diff/
    ├── participants.tsv              # Participant metadata (✓ exists)
    ├── participants.json             # Field definitions (✓ exists)
    ├── audio/                        # Audio files directory
    │   ├── CHI001_recording1.wav     # Audio files with participant IDs
    │   ├── CHI001_recording2.wav
    │   ├── ATL002_recording1.wav
    │   └── ...
    ├── config.py                     # Configuration management (✓ created)
    ├── data_loader.py                # Data loading (✓ created)
    ├── audio_processor.py            # Audio processing (✓ created)
    ├── classifier.py                 # ML models (✓ created)
    ├── main_pipeline.py              # Main pipeline (✓ created)
    ├── requirements.txt              # Dependencies (✓ created)
    ├── pipeline_config.json          # Configuration file (✓ created)
    └── output/                       # Generated outputs
        ├── participants_at_risk_groups.csv
        ├── participant_embeddings.npz
        └── trained_model.joblib
    """
    
    print(structure)


def show_installation_instructions():
    """Show installation instructions."""
    print("\n=== INSTALLATION INSTRUCTIONS ===")
    
    instructions = """
    1. Install Python dependencies:
       pip install -r requirements.txt
    
    2. For GPU support (recommended):
       pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
    
    3. Verify installation:
       python test_pipeline.py
    
    4. Add audio files to the audio/ directory with participant IDs in filenames
    
    5. Run the pipeline:
       python main_pipeline.py
    """
    
    print(instructions)


def main():
    """Run all examples."""
    print("AUDIO AT-RISK CLASSIFICATION PIPELINE")
    print("Example Usage and Documentation")
    print("=" * 60)
    
    # Run examples
    example_data_analysis()
    example_custom_configuration()
    example_basic_usage()
    example_step_by_step()
    example_model_comparison()
    
    # Show structure and instructions
    show_expected_directory_structure()
    show_installation_instructions()
    
    print("\n" + "=" * 60)
    print("EXAMPLES COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    main()
