"""
Checkpoint and state management for the audio classification pipeline.
Implements robust checkpointing to allow resumption from any point.
"""

import json
import pickle
import logging
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import numpy as np
import pandas as pd

from config import ConfigManager


class CheckpointManager:
    """Manages checkpoints and state persistence for the pipeline."""

    def __init__(self, config_manager: ConfigManager, checkpoint_dir: Optional[Path] = None):
        """
        Initialize checkpoint manager.

        Args:
            config_manager: Configuration manager instance
            checkpoint_dir: Optional custom checkpoint directory
        """
        self.config = config_manager

        if checkpoint_dir is None:
            self.checkpoint_dir = self.config.path_config.output_dir / "checkpoints"
        else:
            self.checkpoint_dir = checkpoint_dir

        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)

        self.logger = logging.getLogger(__name__)

        # Define checkpoint files
        self.state_file = self.checkpoint_dir / "pipeline_state.json"
        self.metadata_file = self.checkpoint_dir / "participant_metadata.pkl"
        self.audio_files_file = self.checkpoint_dir / "audio_files_mapping.pkl"
        self.embeddings_file = self.checkpoint_dir / "participant_embeddings.npz"
        self.labels_file = self.checkpoint_dir / "participant_labels.pkl"
        self.model_file = self.checkpoint_dir / "trained_model.pkl"
        self.results_file = self.checkpoint_dir / "training_results.json"

        # Initialize state
        self.state = self._load_state()

    def _load_state(self) -> Dict[str, Any]:
        """Load pipeline state from checkpoint."""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                self.logger.info(f"Loaded pipeline state from {self.state_file}")
                return state
            except Exception as e:
                self.logger.warning(f"Failed to load state: {e}")

        # Default state
        return {
            'pipeline_version': '1.0',
            'created_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'completed_steps': [],
            'participant_count': 0,
            'config_hash': self._get_config_hash(),
            'step_metadata': {}
        }

    def _save_state(self):
        """Save current pipeline state."""
        self.state['last_updated'] = datetime.now().isoformat()
        self.state['config_hash'] = self._get_config_hash()

        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.state, f, indent=2)
            self.logger.debug(f"Saved pipeline state to {self.state_file}")
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")

    def _get_config_hash(self) -> str:
        """Get hash of current configuration for change detection."""
        config_str = json.dumps({
            'audio_config': {
                'sample_rate': self.config.audio_config.sample_rate,
                'apply_vad': self.config.audio_config.apply_vad,
                'vad_threshold': self.config.audio_config.vad_threshold,
                'embedding_model': self.config.audio_config.embedding_model
            },
            'model_config': {
                'classifier_type': self.config.model_config.classifier_type,
                'test_size': self.config.model_config.test_size,
                'random_state': self.config.model_config.random_state
            }
        }, sort_keys=True)

        return hashlib.md5(config_str.encode()).hexdigest()

    def is_step_completed(self, step_name: str) -> bool:
        """Check if a pipeline step has been completed."""
        return step_name in self.state.get('completed_steps', [])

    def mark_step_completed(self, step_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Mark a pipeline step as completed."""
        if 'completed_steps' not in self.state:
            self.state['completed_steps'] = []

        if step_name not in self.state['completed_steps']:
            self.state['completed_steps'].append(step_name)

        if metadata:
            if 'step_metadata' not in self.state:
                self.state['step_metadata'] = {}
            self.state['step_metadata'][step_name] = metadata

        self._save_state()
        self.logger.info(f"Marked step '{step_name}' as completed")

    def should_recompute_step(self, step_name: str, force_recompute: bool = False) -> bool:
        """
        Determine if a step should be recomputed.

        Args:
            step_name: Name of the pipeline step
            force_recompute: Whether to force recomputation

        Returns:
            True if step should be recomputed, False if can use cached results
        """
        if force_recompute:
            return True

        if not self.is_step_completed(step_name):
            return True

        # Check if configuration has changed
        current_hash = self._get_config_hash()
        stored_hash = self.state.get('config_hash')

        if current_hash != stored_hash:
            self.logger.info(f"Configuration changed, recomputing {step_name}")
            return True

        return False

    def save_participant_metadata(self, metadata_df: pd.DataFrame):
        """Save participant metadata."""
        try:
            metadata_df.to_pickle(self.metadata_file)
            self.mark_step_completed('load_metadata', {
                'participant_count': len(metadata_df),
                'file_path': str(self.metadata_file)
            })
            self.logger.info(f"Saved participant metadata: {len(metadata_df)} participants")
        except Exception as e:
            self.logger.error(f"Failed to save participant metadata: {e}")

    def load_participant_metadata(self) -> Optional[pd.DataFrame]:
        """Load participant metadata from checkpoint."""
        if self.metadata_file.exists():
            try:
                df = pd.read_pickle(self.metadata_file)
                self.logger.info(f"Loaded participant metadata: {len(df)} participants")
                return df
            except Exception as e:
                self.logger.error(f"Failed to load participant metadata: {e}")
        return None

    def save_audio_files_mapping(self, audio_files_mapping: Dict[str, List[Path]]):
        """Save audio files mapping."""
        try:
            # Convert Path objects to strings for serialization
            serializable_mapping = {
                pid: [str(path) for path in paths]
                for pid, paths in audio_files_mapping.items()
            }

            with open(self.audio_files_file, 'wb') as f:
                pickle.dump(serializable_mapping, f)

            total_files = sum(len(files) for files in audio_files_mapping.values())
            self.mark_step_completed('discover_audio', {
                'participant_count': len(audio_files_mapping),
                'total_files': total_files,
                'file_path': str(self.audio_files_file)
            })
            self.logger.info(f"Saved audio files mapping: {len(audio_files_mapping)} participants, {total_files} files")
        except Exception as e:
            self.logger.error(f"Failed to save audio files mapping: {e}")

    def load_audio_files_mapping(self) -> Optional[Dict[str, List[Path]]]:
        """Load audio files mapping from checkpoint."""
        if self.audio_files_file.exists():
            try:
                with open(self.audio_files_file, 'rb') as f:
                    serializable_mapping = pickle.load(f)

                # Convert strings back to Path objects
                audio_files_mapping = {
                    pid: [Path(path) for path in paths]
                    for pid, paths in serializable_mapping.items()
                }

                total_files = sum(len(files) for files in audio_files_mapping.values())
                self.logger.info(f"Loaded audio files mapping: {len(audio_files_mapping)} participants, {total_files} files")
                return audio_files_mapping
            except Exception as e:
                self.logger.error(f"Failed to load audio files mapping: {e}")
        return None

    def save_embeddings(self, participant_embeddings: Dict[str, np.ndarray]):
        """Save participant embeddings."""
        try:
            participant_ids = list(participant_embeddings.keys())
            embeddings_array = np.array(list(participant_embeddings.values()))

            np.savez_compressed(
                self.embeddings_file,
                participant_ids=participant_ids,
                embeddings=embeddings_array
            )

            self.mark_step_completed('extract_embeddings', {
                'participant_count': len(participant_embeddings),
                'embedding_dim': embeddings_array.shape[1] if len(embeddings_array) > 0 else 0,
                'file_path': str(self.embeddings_file)
            })
            self.logger.info(f"Saved embeddings: {len(participant_embeddings)} participants")
        except Exception as e:
            self.logger.error(f"Failed to save embeddings: {e}")

    def load_embeddings(self) -> Optional[Dict[str, np.ndarray]]:
        """Load participant embeddings from checkpoint."""
        if self.embeddings_file.exists():
            try:
                data = np.load(self.embeddings_file)
                participant_ids = data['participant_ids']
                embeddings = data['embeddings']

                participant_embeddings = {
                    pid: emb for pid, emb in zip(participant_ids, embeddings)
                }

                self.logger.info(f"Loaded embeddings: {len(participant_embeddings)} participants")
                return participant_embeddings
            except Exception as e:
                self.logger.error(f"Failed to load embeddings: {e}")
        return None

    def save_labels(self, participant_labels: Dict[str, int]):
        """Save participant labels."""
        try:
            with open(self.labels_file, 'wb') as f:
                pickle.dump(participant_labels, f)

            self.mark_step_completed('process_labels', {
                'participant_count': len(participant_labels),
                'file_path': str(self.labels_file)
            })
            self.logger.info(f"Saved labels: {len(participant_labels)} participants")
        except Exception as e:
            self.logger.error(f"Failed to save labels: {e}")

    def load_labels(self) -> Optional[Dict[str, int]]:
        """Load participant labels from checkpoint."""
        if self.labels_file.exists():
            try:
                with open(self.labels_file, 'rb') as f:
                    participant_labels = pickle.load(f)
                self.logger.info(f"Loaded labels: {len(participant_labels)} participants")
                return participant_labels
            except Exception as e:
                self.logger.error(f"Failed to load labels: {e}")
        return None

    def save_model_and_results(self, model_data: Dict[str, Any], training_results: Dict[str, Any]):
        """Save trained model and results."""
        try:
            # Save model
            with open(self.model_file, 'wb') as f:
                pickle.dump(model_data, f)

            # Save results (make numpy arrays JSON serializable)
            serializable_results = self._make_json_serializable(training_results)
            with open(self.results_file, 'w') as f:
                json.dump(serializable_results, f, indent=2)

            self.mark_step_completed('train_model', {
                'test_accuracy': training_results.get('test_accuracy', 0),
                'cv_mean': training_results.get('cv_mean', 0),
                'model_file': str(self.model_file),
                'results_file': str(self.results_file)
            })
            self.logger.info("Saved trained model and results")
        except Exception as e:
            self.logger.error(f"Failed to save model and results: {e}")

    def load_model_and_results(self) -> Optional[tuple]:
        """Load trained model and results from checkpoint."""
        if self.model_file.exists() and self.results_file.exists():
            try:
                # Load model
                with open(self.model_file, 'rb') as f:
                    model_data = pickle.load(f)

                # Load results
                with open(self.results_file, 'r') as f:
                    training_results = json.load(f)

                self.logger.info("Loaded trained model and results")
                return model_data, training_results
            except Exception as e:
                self.logger.error(f"Failed to load model and results: {e}")
        return None

    def _make_json_serializable(self, obj: Any) -> Any:
        """Convert numpy arrays and other non-serializable objects to JSON-serializable format."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        else:
            return obj

    def get_checkpoint_summary(self) -> Dict[str, Any]:
        """Get summary of current checkpoint state."""
        summary = {
            'checkpoint_dir': str(self.checkpoint_dir),
            'state': self.state.copy(),
            'available_checkpoints': {}
        }

        # Check which checkpoint files exist
        checkpoint_files = {
            'metadata': self.metadata_file,
            'audio_files': self.audio_files_file,
            'embeddings': self.embeddings_file,
            'labels': self.labels_file,
            'model': self.model_file,
            'results': self.results_file
        }

        for name, file_path in checkpoint_files.items():
            if file_path.exists():
                stat = file_path.stat()
                summary['available_checkpoints'][name] = {
                    'exists': True,
                    'size_mb': round(stat.st_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
            else:
                summary['available_checkpoints'][name] = {'exists': False}

        return summary

    def clear_checkpoints(self, step_name: Optional[str] = None):
        """
        Clear checkpoints for a specific step or all steps.

        Args:
            step_name: Optional step name to clear, or None to clear all
        """
        if step_name is None:
            # Clear all checkpoints
            for file_path in [self.metadata_file, self.audio_files_file, self.embeddings_file,
                            self.labels_file, self.model_file, self.results_file]:
                if file_path.exists():
                    file_path.unlink()

            # Reset state
            self.state = {
                'pipeline_version': '1.0',
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat(),
                'completed_steps': [],
                'participant_count': 0,
                'config_hash': self._get_config_hash(),
                'step_metadata': {}
            }
            self._save_state()
            self.logger.info("Cleared all checkpoints")
        else:
            # Clear specific step
            step_files = {
                'load_metadata': [self.metadata_file],
                'discover_audio': [self.audio_files_file],
                'extract_embeddings': [self.embeddings_file],
                'process_labels': [self.labels_file],
                'train_model': [self.model_file, self.results_file]
            }

            if step_name in step_files:
                for file_path in step_files[step_name]:
                    if file_path.exists():
                        file_path.unlink()

                # Remove from completed steps
                if step_name in self.state.get('completed_steps', []):
                    self.state['completed_steps'].remove(step_name)

                # Remove step metadata
                if step_name in self.state.get('step_metadata', {}):
                    del self.state['step_metadata'][step_name]

                self._save_state()
                self.logger.info(f"Cleared checkpoint for step: {step_name}")
            else:
                # For any other step name, just remove it from completed steps
                if step_name in self.state.get('completed_steps', []):
                    self.state['completed_steps'].remove(step_name)

                if step_name in self.state.get('step_metadata', {}):
                    del self.state['step_metadata'][step_name]

                self._save_state()
                self.logger.info(f"Cleared checkpoint for step: {step_name}")

    def print_checkpoint_status(self):
        """Print current checkpoint status."""
        summary = self.get_checkpoint_summary()

        print("\n" + "="*60)
        print("CHECKPOINT STATUS")
        print("="*60)

        print(f"Checkpoint directory: {summary['checkpoint_dir']}")
        print(f"Last updated: {summary['state']['last_updated']}")
        print(f"Completed steps: {len(summary['state']['completed_steps'])}")

        if summary['state']['completed_steps']:
            print("  - " + "\n  - ".join(summary['state']['completed_steps']))

        print("\nAvailable checkpoints:")
        for name, info in summary['available_checkpoints'].items():
            if info['exists']:
                print(f"  ✓ {name}: {info['size_mb']} MB (modified: {info['modified']})")
            else:
                print(f"  ✗ {name}: Not available")

        print("="*60)
