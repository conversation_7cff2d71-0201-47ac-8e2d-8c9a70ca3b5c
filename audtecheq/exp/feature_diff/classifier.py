"""
Classification module for at-risk group prediction using various ML algorithms.
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
import joblib
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("Warning: XGBoost not available. Install with: pip install xgboost")

from config import ConfigManager


class AtRiskClassifier:
    """Main classifier for at-risk group prediction."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the classifier.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_names = None
        self.class_names = None

        self.logger = logging.getLogger(__name__)

        # Initialize the appropriate model
        self._initialize_model()

    def _initialize_model(self):
        """Initialize the ML model based on configuration."""
        model_type = self.config.model_config.classifier_type.lower()

        if model_type == "xgboost":
            if not XGBOOST_AVAILABLE:
                self.logger.warning("XGBoost not available, falling back to Random Forest")
                model_type = "random_forest"
                self.model = RandomForestClassifier(**self.config.model_config.rf_params)
            else:
                self.model = xgb.XGBClassifier(**self.config.model_config.xgb_params)

        elif model_type == "random_forest":
            self.model = RandomForestClassifier(**self.config.model_config.rf_params)

        elif model_type == "logistic_regression":
            self.model = LogisticRegression(**self.config.model_config.lr_params)

        else:
            raise ValueError(f"Unknown classifier type: {model_type}")

        self.logger.info(f"Initialized {model_type} classifier")

    def prepare_data(
        self,
        participant_embeddings: Dict[str, np.ndarray],
        participant_labels: Dict[str, int],
        exclude_missing: bool = True
    ) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Prepare data for training/prediction.

        Args:
            participant_embeddings: Dictionary mapping participant IDs to embeddings
            participant_labels: Dictionary mapping participant IDs to labels
            exclude_missing: Whether to exclude participants with missing labels

        Returns:
            Tuple of (features, labels, participant_ids)
        """
        # Find participants with both embeddings and labels
        common_participants = set(participant_embeddings.keys()) & set(participant_labels.keys())

        if exclude_missing:
            # Exclude participants with missing labels (label == -1)
            common_participants = {
                pid for pid in common_participants
                if participant_labels[pid] != -1
            }

        if not common_participants:
            raise ValueError("No participants with both embeddings and labels found")

        # Sort for consistent ordering
        participant_ids = sorted(list(common_participants))

        # Extract features and labels
        features = np.array([participant_embeddings[pid] for pid in participant_ids])
        labels = np.array([participant_labels[pid] for pid in participant_ids])

        self.logger.info(f"Prepared data for {len(participant_ids)} participants")
        self.logger.info(f"Feature shape: {features.shape}")
        self.logger.info(f"Label distribution: {np.bincount(labels)}")

        return features, labels, participant_ids

    def train(
        self,
        features: np.ndarray,
        labels: np.ndarray,
        participant_ids: List[str] = None
    ) -> Dict[str, Any]:
        """
        Train the classifier.

        Args:
            features: Feature matrix
            labels: Target labels
            participant_ids: Optional list of participant IDs for tracking

        Returns:
            Dictionary with training results
        """
        # Encode labels
        labels_encoded = self.label_encoder.fit_transform(labels)
        self.class_names = self.label_encoder.classes_

        # Scale features
        features_scaled = self.scaler.fit_transform(features)

        # Split data - adjust test size for small datasets
        n_samples = len(features_scaled)
        n_classes = len(np.unique(labels_encoded))

        # Ensure test size allows for stratified sampling
        min_test_size = max(n_classes, 2)  # At least one sample per class, minimum 2
        test_size = self.config.model_config.test_size

        if n_samples * test_size < min_test_size:
            # Adjust test size for small datasets
            test_size = min_test_size / n_samples
            test_size = min(test_size, 0.5)  # Don't exceed 50%
            self.logger.warning(f"Adjusted test_size to {test_size:.2f} for small dataset")

        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled,
            labels_encoded,
            test_size=test_size,
            random_state=self.config.model_config.random_state,
            stratify=labels_encoded if n_samples >= 2 * n_classes else None
        )

        # Train model
        self.logger.info("Training classifier...")
        self.model.fit(X_train, y_train)

        # Evaluate on test set
        y_pred = self.model.predict(X_test)
        test_accuracy = accuracy_score(y_test, y_pred)

        # Cross-validation - adjust folds for small datasets
        cv_folds = self.config.model_config.cross_validation_folds
        min_class_size = np.min(np.bincount(labels_encoded))

        if cv_folds > min_class_size:
            cv_folds = max(min_class_size, 2)  # At least 2 folds
            self.logger.warning(f"Adjusted CV folds to {cv_folds} for small dataset")

        if n_samples < cv_folds:
            cv_folds = max(n_samples, 2)
            self.logger.warning(f"Adjusted CV folds to {cv_folds} due to small sample size")

        cv_scores = cross_val_score(
            self.model,
            features_scaled,
            labels_encoded,
            cv=StratifiedKFold(
                n_splits=cv_folds,
                shuffle=True,
                random_state=self.config.model_config.random_state
            ) if n_samples >= 2 * n_classes else cv_folds,
            scoring='accuracy'
        )

        results = {
            'test_accuracy': test_accuracy,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'cv_scores': cv_scores,
            'classification_report': classification_report(
                y_test, y_pred,
                target_names=[str(cls) for cls in self.class_names],
                output_dict=True
            ),
            'confusion_matrix': confusion_matrix(y_test, y_pred)
        }

        self.logger.info(f"Training completed. Test accuracy: {test_accuracy:.3f}")
        self.logger.info(f"CV accuracy: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

        return results

    def predict(self, features: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions on new data.

        Args:
            features: Feature matrix

        Returns:
            Tuple of (predictions, probabilities)
        """
        if self.model is None:
            raise ValueError("Model not trained yet")

        # Scale features
        features_scaled = self.scaler.transform(features)

        # Make predictions
        predictions = self.model.predict(features_scaled)
        probabilities = self.model.predict_proba(features_scaled)

        # Decode labels
        predictions_decoded = self.label_encoder.inverse_transform(predictions)

        return predictions_decoded, probabilities

    def get_feature_importance(self) -> Optional[np.ndarray]:
        """
        Get feature importance if available.

        Returns:
            Feature importance array or None
        """
        if self.model is None:
            return None

        if hasattr(self.model, 'feature_importances_'):
            return self.model.feature_importances_
        elif hasattr(self.model, 'coef_'):
            return np.abs(self.model.coef_[0])
        else:
            return None

    def save_model(self, output_path: Optional[Path] = None):
        """
        Save the trained model and preprocessing objects.

        Args:
            output_path: Optional custom output path
        """
        if output_path is None:
            output_path = self.config.path_config.output_dir / "trained_model.joblib"

        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'class_names': self.class_names,
            'config': self.config
        }

        joblib.dump(model_data, output_path)
        self.logger.info(f"Model saved to {output_path}")

    def load_model(self, input_path: Path):
        """
        Load a trained model and preprocessing objects.

        Args:
            input_path: Path to saved model
        """
        model_data = joblib.load(input_path)

        self.model = model_data['model']
        self.scaler = model_data['scaler']
        self.label_encoder = model_data['label_encoder']
        self.class_names = model_data['class_names']

        self.logger.info(f"Model loaded from {input_path}")


class ModelEvaluator:
    """Evaluates and compares different models."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the evaluator.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)

    def compare_models(
        self,
        features: np.ndarray,
        labels: np.ndarray,
        model_types: List[str] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Compare different model types.

        Args:
            features: Feature matrix
            labels: Target labels
            model_types: List of model types to compare

        Returns:
            Dictionary with results for each model type
        """
        if model_types is None:
            model_types = ["random_forest", "logistic_regression"]
            if XGBOOST_AVAILABLE:
                model_types.append("xgboost")

        results = {}

        for model_type in model_types:
            self.logger.info(f"Evaluating {model_type}...")

            # Create temporary config for this model
            temp_config = self.config
            temp_config.model_config.classifier_type = model_type

            # Train and evaluate
            classifier = AtRiskClassifier(temp_config)
            model_results = classifier.train(features, labels)

            results[model_type] = model_results

        return results

    def print_comparison_results(self, results: Dict[str, Dict[str, Any]]):
        """
        Print comparison results in a readable format.

        Args:
            results: Results from compare_models
        """
        print("\n=== MODEL COMPARISON RESULTS ===")
        print(f"{'Model':<20} {'Test Acc':<10} {'CV Mean':<10} {'CV Std':<10}")
        print("-" * 50)

        for model_type, model_results in results.items():
            test_acc = model_results['test_accuracy']
            cv_mean = model_results['cv_mean']
            cv_std = model_results['cv_std']

            print(f"{model_type:<20} {test_acc:<10.3f} {cv_mean:<10.3f} {cv_std:<10.3f}")

        # Find best model
        best_model = max(results.keys(), key=lambda k: results[k]['cv_mean'])
        print(f"\nBest model: {best_model}")
        print(f"Best CV accuracy: {results[best_model]['cv_mean']:.3f} ± {results[best_model]['cv_std']:.3f}")


class PredictionPipeline:
    """End-to-end prediction pipeline."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the pipeline.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.classifier = AtRiskClassifier(config_manager)
        self.logger = logging.getLogger(__name__)

    def run_full_pipeline(
        self,
        participant_embeddings: Dict[str, np.ndarray],
        participant_labels: Dict[str, int]
    ) -> Dict[str, Any]:
        """
        Run the complete training and evaluation pipeline.

        Args:
            participant_embeddings: Dictionary mapping participant IDs to embeddings
            participant_labels: Dictionary mapping participant IDs to labels

        Returns:
            Dictionary with pipeline results
        """
        self.logger.info("Starting full prediction pipeline...")

        # Prepare data
        features, labels, participant_ids = self.classifier.prepare_data(
            participant_embeddings, participant_labels
        )

        # Train classifier
        training_results = self.classifier.train(features, labels, participant_ids)

        # Save model
        self.classifier.save_model()

        # Get feature importance
        feature_importance = self.classifier.get_feature_importance()

        pipeline_results = {
            'training_results': training_results,
            'feature_importance': feature_importance,
            'num_participants': len(participant_ids),
            'num_features': features.shape[1],
            'class_distribution': np.bincount(labels)
        }

        self.logger.info("Pipeline completed successfully")

        return pipeline_results
