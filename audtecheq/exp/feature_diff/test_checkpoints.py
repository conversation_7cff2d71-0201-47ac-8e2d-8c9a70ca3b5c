#!/usr/bin/env python3
"""
Test script for the checkpoint system.
"""

import sys
import logging
from pathlib import Path
import tempfile
import shutil
import pandas as pd
import numpy as np

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from config import ConfigManager
from checkpoint_manager import CheckpointManager


def test_checkpoint_manager():
    """Test the checkpoint manager functionality."""
    print("Testing CheckpointManager...")
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Initialize config and checkpoint manager
        config = ConfigManager()
        config.set_base_directory(temp_path)
        
        checkpoint_manager = CheckpointManager(config, checkpoint_dir=temp_path / "test_checkpoints")
        
        # Test 1: Initial state
        print("✓ Checkpoint manager initialized")
        assert not checkpoint_manager.is_step_completed("test_step")
        
        # Test 2: Mark step completed
        checkpoint_manager.mark_step_completed("test_step", {"test_data": "value"})
        assert checkpoint_manager.is_step_completed("test_step")
        print("✓ Step completion tracking works")
        
        # Test 3: Save and load participant metadata
        test_df = pd.DataFrame({
            'participant_id': ['CHI001', 'CHI002', 'ATL001'],
            'at_risk_group': ['Not at risk', 'At risk for speech disorder', 'Not at risk'],
            'at_risk_code': [4, 2, 4]
        })
        
        checkpoint_manager.save_participant_metadata(test_df)
        loaded_df = checkpoint_manager.load_participant_metadata()
        
        assert loaded_df is not None
        assert len(loaded_df) == 3
        print("✓ Participant metadata save/load works")
        
        # Test 4: Save and load labels
        test_labels = {'CHI001': 4, 'CHI002': 2, 'ATL001': 4}
        checkpoint_manager.save_labels(test_labels)
        loaded_labels = checkpoint_manager.load_labels()
        
        assert loaded_labels == test_labels
        print("✓ Labels save/load works")
        
        # Test 5: Save and load embeddings
        test_embeddings = {
            'CHI001': np.random.rand(768),
            'CHI002': np.random.rand(768),
            'ATL001': np.random.rand(768)
        }
        
        checkpoint_manager.save_embeddings(test_embeddings)
        loaded_embeddings = checkpoint_manager.load_embeddings()
        
        assert loaded_embeddings is not None
        assert len(loaded_embeddings) == 3
        assert 'CHI001' in loaded_embeddings
        print("✓ Embeddings save/load works")
        
        # Test 6: Save and load audio files mapping
        test_audio_files = {
            'CHI001': [Path('audio1.wav'), Path('audio2.wav')],
            'CHI002': [Path('audio3.wav')],
            'ATL001': [Path('audio4.wav'), Path('audio5.wav')]
        }
        
        checkpoint_manager.save_audio_files_mapping(test_audio_files)
        loaded_audio_files = checkpoint_manager.load_audio_files_mapping()
        
        assert loaded_audio_files is not None
        assert len(loaded_audio_files) == 3
        assert len(loaded_audio_files['CHI001']) == 2
        print("✓ Audio files mapping save/load works")
        
        # Test 7: Save and load model results
        test_model_data = {'model_type': 'xgboost', 'trained': True}
        test_results = {
            'test_accuracy': 0.85,
            'cv_mean': 0.82,
            'cv_std': 0.05,
            'confusion_matrix': np.array([[10, 2], [1, 8]])
        }
        
        checkpoint_manager.save_model_and_results(test_model_data, test_results)
        loaded_data = checkpoint_manager.load_model_and_results()
        
        assert loaded_data is not None
        model_data, results = loaded_data
        assert model_data['model_type'] == 'xgboost'
        assert results['test_accuracy'] == 0.85
        print("✓ Model and results save/load works")
        
        # Test 8: Checkpoint summary
        summary = checkpoint_manager.get_checkpoint_summary()
        assert 'checkpoint_dir' in summary
        assert 'state' in summary
        assert 'available_checkpoints' in summary
        print("✓ Checkpoint summary works")
        
        # Test 9: Clear checkpoints
        checkpoint_manager.clear_checkpoints("test_step")
        assert not checkpoint_manager.is_step_completed("test_step")
        print("✓ Checkpoint clearing works")
        
        print("✓ All checkpoint manager tests passed!")


def test_checkpoint_integration():
    """Test checkpoint integration with pipeline components."""
    print("\nTesting checkpoint integration...")
    
    # This would test the integration with the actual pipeline
    # For now, we'll just verify the checkpoint files are created correctly
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        config = ConfigManager()
        config.set_base_directory(temp_path)
        
        checkpoint_manager = CheckpointManager(config, checkpoint_dir=temp_path / "integration_test")
        
        # Simulate pipeline steps
        steps = ["load_metadata", "discover_audio", "extract_embeddings", "train_model"]
        
        for step in steps:
            # Check that step is not completed initially
            assert not checkpoint_manager.is_step_completed(step)
            
            # Mark as completed
            checkpoint_manager.mark_step_completed(step, {"step": step, "timestamp": "test"})
            
            # Verify it's marked as completed
            assert checkpoint_manager.is_step_completed(step)
        
        # Check that all steps are completed
        completed_steps = checkpoint_manager.state.get('completed_steps', [])
        assert len(completed_steps) == 4
        assert all(step in completed_steps for step in steps)
        
        print("✓ Pipeline step integration works")
        
        # Test configuration change detection
        original_hash = checkpoint_manager._get_config_hash()
        
        # Simulate configuration change
        config.audio_config.sample_rate = 22050  # Change from default 16000
        checkpoint_manager.config = config
        
        new_hash = checkpoint_manager._get_config_hash()
        assert original_hash != new_hash
        
        # Should require recomputation due to config change
        assert checkpoint_manager.should_recompute_step("extract_embeddings", force_recompute=False)
        
        print("✓ Configuration change detection works")
        
        print("✓ All integration tests passed!")


def test_checkpoint_status_display():
    """Test checkpoint status display functionality."""
    print("\nTesting checkpoint status display...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        config = ConfigManager()
        config.set_base_directory(temp_path)
        
        checkpoint_manager = CheckpointManager(config, checkpoint_dir=temp_path / "status_test")
        
        # Create some test checkpoints
        checkpoint_manager.mark_step_completed("load_metadata", {"participants": 100})
        checkpoint_manager.mark_step_completed("discover_audio", {"files": 500})
        
        # Create some dummy checkpoint files
        (checkpoint_manager.checkpoint_dir / "participant_metadata.pkl").touch()
        (checkpoint_manager.checkpoint_dir / "audio_files_mapping.pkl").touch()
        
        # Test status display (this will print to console)
        print("Testing status display:")
        checkpoint_manager.print_checkpoint_status()
        
        print("✓ Status display test completed")


def run_all_tests():
    """Run all checkpoint tests."""
    print("="*60)
    print("CHECKPOINT SYSTEM TESTS")
    print("="*60)
    
    # Set up logging to reduce noise during testing
    logging.getLogger().setLevel(logging.WARNING)
    
    try:
        test_checkpoint_manager()
        test_checkpoint_integration()
        test_checkpoint_status_display()
        
        print("\n" + "="*60)
        print("ALL CHECKPOINT TESTS PASSED!")
        print("="*60)
        
        print("\nCheckpoint system is ready for use!")
        print("Key features verified:")
        print("✓ Automatic state persistence")
        print("✓ Step completion tracking")
        print("✓ Data serialization/deserialization")
        print("✓ Configuration change detection")
        print("✓ Selective checkpoint clearing")
        print("✓ Status reporting")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
