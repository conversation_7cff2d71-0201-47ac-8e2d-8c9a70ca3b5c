#!/usr/bin/env python3
"""
Main pipeline for audio-based at-risk classification.
Orchestrates the entire workflow from data loading to model training and evaluation.
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import numpy as np

# Import our modules
from config import ConfigManager
from data_loader import ParticipantDataLoader, AudioFileManager
from audio_processor import ParticipantEmbeddingAggregator
from classifier import AtRiskClassifier, ModelEvaluator, PredictionPipeline


class AudioAtRiskPipeline:
    """Main pipeline class that orchestrates the entire workflow."""
    
    def __init__(self, base_dir: Path, config_file: Optional[Path] = None):
        """
        Initialize the pipeline.
        
        Args:
            base_dir: Base directory containing data files
            config_file: Optional configuration file path
        """
        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Initialize configuration
        self.config_manager = ConfigManager(config_file)
        self.config_manager.set_base_directory(base_dir)
        
        # Validate configuration
        try:
            self.config_manager.validate()
        except ValueError as e:
            self.logger.error(f"Configuration validation failed: {e}")
            sys.exit(1)
        
        # Initialize components
        self.data_loader = ParticipantDataLoader(self.config_manager)
        self.audio_manager = AudioFileManager(self.config_manager)
        self.embedding_aggregator = ParticipantEmbeddingAggregator(self.config_manager)
        self.prediction_pipeline = PredictionPipeline(self.config_manager)
        
        self.logger.info("Pipeline initialized successfully")
    
    def load_participant_data(self) -> Dict[str, int]:
        """
        Load and process participant metadata.
        
        Returns:
            Dictionary mapping participant IDs to labels
        """
        self.logger.info("Loading participant data...")
        
        # Load metadata
        self.data_loader.load_participant_metadata()
        self.data_loader.load_field_mappings()
        
        # Create at-risk groups
        at_risk_df = self.data_loader.create_at_risk_groups()
        
        # Save processed data
        self.data_loader.save_processed_data()
        
        # Get classification data
        participant_ids, labels = self.data_loader.get_classification_data(exclude_missing=True)
        
        # Create mapping
        participant_labels = dict(zip(participant_ids, labels))
        
        # Print label distribution
        label_dist = self.data_loader.get_label_distribution()
        self.logger.info("Label distribution:")
        for label, count in label_dist.items():
            self.logger.info(f"  {label}: {count}")
        
        return participant_labels
    
    def discover_audio_files(self) -> Dict[str, list]:
        """
        Discover audio files for participants.
        
        Returns:
            Dictionary mapping participant IDs to audio file lists
        """
        self.logger.info("Discovering audio files...")
        
        audio_files = self.audio_manager.discover_audio_files()
        
        if not audio_files:
            self.logger.warning("No audio files found!")
            return {}
        
        # Log statistics
        total_files = sum(len(files) for files in audio_files.values())
        self.logger.info(f"Found {total_files} audio files for {len(audio_files)} participants")
        
        # Show some examples
        for i, (participant_id, files) in enumerate(list(audio_files.items())[:3]):
            self.logger.info(f"  {participant_id}: {len(files)} files")
        
        return audio_files
    
    def extract_embeddings(
        self, 
        participant_audio_files: Dict[str, list],
        force_recompute: bool = False
    ) -> Dict[str, np.ndarray]:
        """
        Extract and aggregate embeddings for all participants.
        
        Args:
            participant_audio_files: Dictionary mapping participant IDs to audio files
            force_recompute: Whether to force recomputation of embeddings
            
        Returns:
            Dictionary mapping participant IDs to aggregated embeddings
        """
        embeddings_file = self.config_manager.path_config.output_dir / "participant_embeddings.npz"
        
        # Check if embeddings already exist
        if embeddings_file.exists() and not force_recompute:
            self.logger.info("Loading existing embeddings...")
            try:
                participant_embeddings = self.embedding_aggregator.load_embeddings(embeddings_file)
                self.logger.info(f"Loaded embeddings for {len(participant_embeddings)} participants")
                return participant_embeddings
            except Exception as e:
                self.logger.warning(f"Failed to load existing embeddings: {e}")
                self.logger.info("Computing embeddings from scratch...")
        
        # Extract embeddings
        self.logger.info("Extracting audio embeddings...")
        participant_embeddings = self.embedding_aggregator.process_all_participants(
            participant_audio_files
        )
        
        # Save embeddings
        if participant_embeddings:
            self.embedding_aggregator.save_embeddings(participant_embeddings)
        
        return participant_embeddings
    
    def train_and_evaluate(
        self,
        participant_embeddings: Dict[str, np.ndarray],
        participant_labels: Dict[str, int]
    ) -> Dict[str, Any]:
        """
        Train and evaluate the classification model.
        
        Args:
            participant_embeddings: Dictionary mapping participant IDs to embeddings
            participant_labels: Dictionary mapping participant IDs to labels
            
        Returns:
            Dictionary with training and evaluation results
        """
        self.logger.info("Training and evaluating classifier...")
        
        # Run the full prediction pipeline
        results = self.prediction_pipeline.run_full_pipeline(
            participant_embeddings, participant_labels
        )
        
        # Print results
        self._print_results(results)
        
        return results
    
    def compare_models(
        self,
        participant_embeddings: Dict[str, np.ndarray],
        participant_labels: Dict[str, int]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Compare different model types.
        
        Args:
            participant_embeddings: Dictionary mapping participant IDs to embeddings
            participant_labels: Dictionary mapping participant IDs to labels
            
        Returns:
            Dictionary with comparison results
        """
        self.logger.info("Comparing different models...")
        
        # Prepare data
        classifier = AtRiskClassifier(self.config_manager)
        features, labels, participant_ids = classifier.prepare_data(
            participant_embeddings, participant_labels
        )
        
        # Compare models
        evaluator = ModelEvaluator(self.config_manager)
        comparison_results = evaluator.compare_models(features, labels)
        
        # Print comparison
        evaluator.print_comparison_results(comparison_results)
        
        return comparison_results
    
    def run_full_pipeline(
        self, 
        force_recompute_embeddings: bool = False,
        compare_models: bool = True
    ) -> Dict[str, Any]:
        """
        Run the complete pipeline from start to finish.
        
        Args:
            force_recompute_embeddings: Whether to force recomputation of embeddings
            compare_models: Whether to compare different model types
            
        Returns:
            Dictionary with all pipeline results
        """
        self.logger.info("Starting full audio at-risk classification pipeline...")
        
        try:
            # Step 1: Load participant data
            participant_labels = self.load_participant_data()
            
            # Step 2: Discover audio files
            participant_audio_files = self.discover_audio_files()
            
            if not participant_audio_files:
                self.logger.error("No audio files found. Cannot proceed.")
                return {}
            
            # Step 3: Extract embeddings
            participant_embeddings = self.extract_embeddings(
                participant_audio_files, 
                force_recompute=force_recompute_embeddings
            )
            
            if not participant_embeddings:
                self.logger.error("No embeddings extracted. Cannot proceed.")
                return {}
            
            # Step 4: Train and evaluate
            training_results = self.train_and_evaluate(
                participant_embeddings, participant_labels
            )
            
            # Step 5: Compare models (optional)
            comparison_results = {}
            if compare_models:
                comparison_results = self.compare_models(
                    participant_embeddings, participant_labels
                )
            
            # Compile final results
            final_results = {
                'training_results': training_results,
                'comparison_results': comparison_results,
                'num_participants_with_labels': len(participant_labels),
                'num_participants_with_audio': len(participant_audio_files),
                'num_participants_with_embeddings': len(participant_embeddings)
            }
            
            self.logger.info("Pipeline completed successfully!")
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise
    
    def _print_results(self, results: Dict[str, Any]):
        """Print training results in a readable format."""
        training_results = results['training_results']
        
        print("\n" + "="*50)
        print("TRAINING RESULTS")
        print("="*50)
        
        print(f"Number of participants: {results['num_participants']}")
        print(f"Number of features: {results['num_features']}")
        print(f"Test accuracy: {training_results['test_accuracy']:.3f}")
        print(f"Cross-validation accuracy: {training_results['cv_mean']:.3f} ± {training_results['cv_std']:.3f}")
        
        print("\nClassification Report:")
        report = training_results['classification_report']
        for class_name, metrics in report.items():
            if isinstance(metrics, dict):
                print(f"  {class_name}: precision={metrics.get('precision', 0):.3f}, "
                      f"recall={metrics.get('recall', 0):.3f}, "
                      f"f1-score={metrics.get('f1-score', 0):.3f}")
        
        print("\nConfusion Matrix:")
        print(training_results['confusion_matrix'])


def main():
    """Main function to run the pipeline."""
    # Get the directory where this script is located
    script_dir = Path(__file__).parent
    
    # You can optionally create a config file
    config_file = script_dir / "pipeline_config.json"
    
    # Initialize and run pipeline
    pipeline = AudioAtRiskPipeline(
        base_dir=script_dir,
        config_file=config_file if config_file.exists() else None
    )
    
    # Run the full pipeline
    results = pipeline.run_full_pipeline(
        force_recompute_embeddings=False,  # Set to True to recompute embeddings
        compare_models=True  # Set to False to skip model comparison
    )
    
    return results


if __name__ == "__main__":
    results = main()
