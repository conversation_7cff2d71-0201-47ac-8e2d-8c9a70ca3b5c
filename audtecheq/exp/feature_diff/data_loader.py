"""
Data loading and management module for participant data and audio files.
"""

import pandas as pd
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging

from config import ConfigManager


class ParticipantDataLoader:
    """Handles loading and processing of participant metadata."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the data loader.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.participants_df = None
        self.field_mappings = None
        self.at_risk_groups = None
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def load_participant_metadata(self) -> pd.DataFrame:
        """
        Load participant metadata from TSV file.
        
        Returns:
            DataFrame with participant data
        """
        try:
            self.participants_df = pd.read_csv(
                self.config.path_config.participants_tsv, 
                sep='\t'
            )
            self.logger.info(f"Loaded {len(self.participants_df)} participants")
            return self.participants_df
        
        except Exception as e:
            self.logger.error(f"Error loading participant data: {e}")
            raise
    
    def load_field_mappings(self) -> Dict[str, Any]:
        """
        Load field mappings from JSON file.
        
        Returns:
            Dictionary containing field mappings
        """
        try:
            with open(self.config.path_config.participants_json, 'r') as f:
                self.field_mappings = json.load(f)
            
            self.logger.info("Field mappings loaded successfully")
            return self.field_mappings
        
        except Exception as e:
            self.logger.error(f"Error loading field mappings: {e}")
            raise
    
    def extract_level_mappings(self, field_name: str) -> Dict[float, str]:
        """
        Extract level mappings for a specific field.
        
        Args:
            field_name: Name of the field to extract mappings for
            
        Returns:
            Dictionary mapping numeric codes to string descriptions
        """
        if self.field_mappings is None:
            self.load_field_mappings()
        
        field_data = self.field_mappings.get(field_name, {})
        if 'Levels' not in field_data:
            return {}
        
        return {float(k): v for k, v in field_data['Levels'].items()}
    
    def create_at_risk_groups(self) -> pd.DataFrame:
        """
        Create at-risk groups from participant data.
        
        Returns:
            DataFrame with processed at-risk group information
        """
        if self.participants_df is None:
            self.load_participant_metadata()
        
        if self.field_mappings is None:
            self.load_field_mappings()
        
        # Extract mappings
        at_risk_mappings = self.extract_level_mappings('at_risk_decision')
        sex_mappings = self.extract_level_mappings('ss_child_biosex')
        hisp_mappings = self.extract_level_mappings('ss_child_hisp_latx')
        
        result_data = []
        
        for _, row in self.participants_df.iterrows():
            participant_id = row['id_number']
            at_risk_code = row['at_risk_decision']
            
            # Handle missing at_risk_decision values
            if pd.isna(at_risk_code):
                at_risk_group = "Missing data"
                at_risk_code_clean = None
                numeric_label = -1  # Special code for missing data
            else:
                at_risk_group = at_risk_mappings.get(float(at_risk_code), "Unknown")
                at_risk_code_clean = int(at_risk_code)
                numeric_label = int(at_risk_code)
            
            # Map other fields
            sex = self._map_field_value(row['ss_child_biosex'], sex_mappings)
            hispanic_latinx = self._map_field_value(row['ss_child_hisp_latx'], hisp_mappings)
            
            result_data.append({
                'participant_id': participant_id,
                'at_risk_group': at_risk_group,
                'at_risk_code': at_risk_code_clean,
                'numeric_label': numeric_label,
                'age_months': row['ss_child_age'] if not pd.isna(row['ss_child_age']) else None,
                'chronological_age': row['ss_child_chronological_age'] if not pd.isna(row['ss_child_chronological_age']) else None,
                'sex': sex,
                'income_status': row['ss_demographic_groups'] if not pd.isna(row['ss_demographic_groups']) else None,
                'hispanic_latinx': hispanic_latinx
            })
        
        self.at_risk_groups = pd.DataFrame(result_data)
        self.logger.info(f"Created at-risk groups for {len(self.at_risk_groups)} participants")
        
        return self.at_risk_groups
    
    def _map_field_value(self, value, mapping_dict: Dict[float, str], default: str = "Unknown") -> str:
        """
        Map a field value using the provided mapping dictionary.
        
        Args:
            value: The value to map
            mapping_dict: Mapping dictionary
            default: Default value if mapping not found
            
        Returns:
            Mapped value or default
        """
        if pd.isna(value):
            return "Unknown"
        return mapping_dict.get(float(value), default)
    
    def get_classification_data(self, exclude_missing: bool = True) -> Tuple[List[str], List[int]]:
        """
        Get participant IDs and labels for classification.
        
        Args:
            exclude_missing: Whether to exclude participants with missing at-risk data
            
        Returns:
            Tuple of (participant_ids, labels)
        """
        if self.at_risk_groups is None:
            self.create_at_risk_groups()
        
        if exclude_missing:
            # Exclude participants with missing data (numeric_label == -1)
            valid_data = self.at_risk_groups[self.at_risk_groups['numeric_label'] != -1]
        else:
            valid_data = self.at_risk_groups
        
        participant_ids = valid_data['participant_id'].tolist()
        labels = valid_data['numeric_label'].tolist()
        
        self.logger.info(f"Classification data: {len(participant_ids)} participants")
        
        return participant_ids, labels
    
    def get_label_distribution(self) -> Dict[str, int]:
        """
        Get the distribution of at-risk labels.
        
        Returns:
            Dictionary with label counts
        """
        if self.at_risk_groups is None:
            self.create_at_risk_groups()
        
        return self.at_risk_groups['at_risk_group'].value_counts().to_dict()
    
    def save_processed_data(self, output_path: Optional[Path] = None):
        """
        Save processed at-risk groups data to CSV.
        
        Args:
            output_path: Optional custom output path
        """
        if self.at_risk_groups is None:
            self.create_at_risk_groups()
        
        if output_path is None:
            output_path = self.config.path_config.output_dir / "participants_at_risk_groups.csv"
        
        # Sort by at_risk_code and participant_id
        sorted_df = self.at_risk_groups.sort_values(['at_risk_code', 'participant_id'])
        sorted_df.to_csv(output_path, index=False)
        
        self.logger.info(f"Saved processed data to {output_path}")
        
        # Print summary
        print("\n=== AT-RISK GROUP SUMMARY ===")
        group_counts = sorted_df['at_risk_group'].value_counts()
        for group, count in group_counts.items():
            print(f"{group}: {count} participants")


class AudioFileManager:
    """Manages audio file discovery and organization."""
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the audio file manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.audio_files = {}  # participant_id -> list of audio file paths
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
    
    def discover_audio_files(self, audio_extensions: List[str] = None) -> Dict[str, List[Path]]:
        """
        Discover audio files for each participant.
        
        Args:
            audio_extensions: List of audio file extensions to search for
            
        Returns:
            Dictionary mapping participant IDs to lists of audio file paths
        """
        if audio_extensions is None:
            audio_extensions = ['.wav', '.mp3', '.flac', '.m4a']
        
        audio_dir = self.config.path_config.audio_dir
        
        if not audio_dir.exists():
            self.logger.warning(f"Audio directory not found: {audio_dir}")
            return {}
        
        # Search for audio files
        for ext in audio_extensions:
            for audio_file in audio_dir.rglob(f"*{ext}"):
                # Extract participant ID from filename
                participant_id = self._extract_participant_id(audio_file.name)
                
                if participant_id:
                    if participant_id not in self.audio_files:
                        self.audio_files[participant_id] = []
                    self.audio_files[participant_id].append(audio_file)
        
        self.logger.info(f"Found audio files for {len(self.audio_files)} participants")
        
        # Log statistics
        total_files = sum(len(files) for files in self.audio_files.values())
        self.logger.info(f"Total audio files: {total_files}")
        
        return self.audio_files
    
    def _extract_participant_id(self, filename: str) -> Optional[str]:
        """
        Extract participant ID from filename.
        
        Args:
            filename: Audio filename
            
        Returns:
            Participant ID if found, None otherwise
        """
        # Common patterns for participant IDs
        # Adjust this based on your actual filename patterns
        import re
        
        # Pattern for IDs like CHI001, ATL002, etc.
        pattern = r'([A-Z]{3}\d{3})'
        match = re.search(pattern, filename.upper())
        
        if match:
            return match.group(1)
        
        return None
    
    def get_participant_audio_files(self, participant_id: str) -> List[Path]:
        """
        Get audio files for a specific participant.
        
        Args:
            participant_id: Participant ID
            
        Returns:
            List of audio file paths for the participant
        """
        return self.audio_files.get(participant_id, [])
    
    def get_participants_with_audio(self) -> List[str]:
        """
        Get list of participant IDs that have audio files.
        
        Returns:
            List of participant IDs with audio data
        """
        return list(self.audio_files.keys())
