# SharePoint Audio Classification Pipeline

This document explains how to use the SharePoint-enabled audio classification pipeline for the SPROUT dataset.

## Overview

The pipeline automatically:
1. **Reads participant metadata** from `participants_at_risk_groups.csv` (272 participants with valid labels)
2. **Downloads audio files** from SharePoint using the structure: `ds-<citycode>/sub-<participantid>/beh/<files>`
3. **Extracts embeddings** using pretrained Wav2Vec2 models
4. **Aggregates per participant** to avoid diarization issues
5. **Trains classifiers** (XGBoost, Random Forest, Logistic Regression) for at-risk prediction

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Test SharePoint Connection

```bash
python test_sharepoint.py \
  --sharepoint-url "https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/" \
  --sharepoint-username "<EMAIL>" \
  --sharepoint-password "Megalophiya*092426" \
  --test-download
```

### 3. Run the Pipeline

```bash
# Simple run with provided credentials
python run_sharepoint_pipeline.py

# Or test connection only
python run_sharepoint_pipeline.py --test-connection

# Or run with custom parameters
python sharepoint_pipeline.py \
  --sharepoint-url "https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/" \
  --sharepoint-username "<EMAIL>" \
  --sharepoint-password "Megalophiya*092426" \
  --max-participants 50 \
  --force-redownload-audio
```

## Expected Data Structure

### SharePoint Organization
```
Shared Documents/Datasets/ds-SPROUT/
├── ds-CHI/          # Chicago participants
│   ├── sub-CHI001/
│   │   └── beh/
│   │       ├── audio1.wav
│   │       └── audio2.wav
│   ├── sub-CHI002/
│   └── ...
├── ds-ATL/          # Atlanta participants
│   ├── sub-ATL001/
│   └── ...
├── ds-BLT/          # Baltimore participants
├── ds-DLS/          # Dallas participants
├── ds-ISN/          # (City code ISN)
├── ds-LAX/          # Los Angeles participants
├── ds-ORL/          # Orlando participants
└── ds-STL/          # St. Louis participants
```

### Local Cache Structure
```
audtecheq/exp/feature_diff/
├── participants_at_risk_groups.csv    # 272 participants with labels
├── sharepoint_cache/                  # Downloaded audio files
│   ├── CHI001/
│   │   ├── audio1.wav
│   │   └── audio2.wav
│   ├── ATL001/
│   └── ...
├── participant_embeddings.npz         # Cached embeddings
└── trained_model.joblib               # Saved model
```

## Participant Distribution

Based on `participants_at_risk_groups.csv`:
- **Total participants**: 426
- **With valid labels**: 272 participants
- **At-risk breakdown**:
  - At risk for language disorder: 30 (11.0%)
  - At risk for speech disorder: 37 (13.6%)
  - At risk for speech and language disorder: 46 (16.9%)
  - Not at risk: 153 (56.3%)
  - Could not be determined: 6 (2.2%)

## Pipeline Features

### 🎯 **Pretrained Embeddings + Aggregation**
- Uses **Wav2Vec2** for robust audio feature extraction
- **Aggregates multiple files per participant** (mean/median/max)
- **Avoids diarization** by statistically learning to ignore adult speech

### 🎯 **Voice Activity Detection**
- **Energy-based VAD** to remove silence
- **Configurable threshold** (default: 0.5)
- **Optional preprocessing** step

### 🎯 **Multiple ML Algorithms**
- **XGBoost** (primary choice for tabular data)
- **Random Forest** (robust ensemble method)
- **Logistic Regression** (interpretable baseline)
- **Automatic model comparison**

### 🎯 **SharePoint Integration**
- **Automatic file discovery** based on participant IDs
- **Intelligent caching** to avoid re-downloading
- **Robust error handling** for missing files
- **Progress tracking** and statistics

## Command Line Options

```bash
python sharepoint_pipeline.py [OPTIONS]

Required:
  --sharepoint-url URL          SharePoint site URL
  --sharepoint-username USER    SharePoint username  
  --sharepoint-password PASS    SharePoint password

Optional:
  --max-participants N          Limit number of participants (default: all)
  --participant-ids ID1 ID2     Process specific participants only
  --force-redownload-audio      Force redownload of audio files
  --force-recompute-embeddings  Force recomputation of embeddings
  --no-model-comparison         Skip model comparison
  --cache-dir DIR               Custom cache directory
  --log-level LEVEL             Logging level (DEBUG/INFO/WARNING/ERROR)
```

## Configuration

### Audio Processing Configuration
```json
{
  "audio": {
    "sample_rate": 16000,
    "max_duration": null,
    "apply_vad": true,
    "vad_threshold": 0.5,
    "embedding_model": "facebook/wav2vec2-base-960h"
  }
}
```

### Model Configuration
```json
{
  "model": {
    "classifier_type": "xgboost",
    "test_size": 0.2,
    "random_state": 42,
    "cross_validation_folds": 5
  }
}
```

## Expected Output

### Console Output
```
SHAREPOINT PIPELINE RESULTS
============================================================
Downloaded audio for: 45 participants
Total audio files: 180
Total cache size: 2.3 GB

TRAINING RESULTS
============================================================
Number of participants: 45
Number of features: 768
Test accuracy: 0.778
Cross-validation accuracy: 0.756 ± 0.089

MODEL COMPARISON RESULTS
Model                Test Acc   CV Mean    CV Std    
--------------------------------------------------
xgboost             0.778      0.756      0.089
random_forest       0.722      0.733      0.095
logistic_regression 0.667      0.689      0.102

Best model: xgboost
Best CV accuracy: 0.756 ± 0.089
```

### Generated Files
- `participants_at_risk_groups.csv` - Processed participant metadata
- `participant_embeddings.npz` - Cached audio embeddings
- `trained_model.joblib` - Saved trained model
- `sharepoint_cache/` - Downloaded audio files

## Troubleshooting

### Common Issues

1. **SharePoint Authentication Failed**
   - Check username/password
   - Verify SharePoint URL
   - Ensure account has access to the site

2. **No Audio Files Found**
   - Check SharePoint path structure
   - Verify participant IDs match folder names
   - Check file permissions

3. **Memory Issues**
   - Reduce `--max-participants`
   - Use CPU instead of GPU for embeddings
   - Clear cache with `pipeline.sharepoint_audio_manager.clear_cache()`

4. **Slow Performance**
   - Use cached files (don't use `--force-redownload-audio`)
   - Use cached embeddings (don't use `--force-recompute-embeddings`)
   - Process participants in batches

### Performance Tips

1. **Start Small**: Begin with `--max-participants 10` to test
2. **Use Caching**: Let the system cache downloads and embeddings
3. **GPU Acceleration**: Install CUDA-enabled PyTorch for faster embedding extraction
4. **Parallel Processing**: The pipeline can be extended for parallel audio processing

## Security Note

The provided credentials are included for demonstration. In production:
1. Use environment variables for credentials
2. Implement proper authentication flows
3. Use service accounts with minimal permissions
4. Consider using Azure AD authentication

## Next Steps

1. **Test with small dataset**: Start with 10-20 participants
2. **Validate results**: Check model performance and feature importance
3. **Scale up**: Gradually increase participant count
4. **Optimize**: Tune hyperparameters and model selection
5. **Deploy**: Create production pipeline with proper security
