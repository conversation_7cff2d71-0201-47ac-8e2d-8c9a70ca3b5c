#!/usr/bin/env python3
"""
Test script for SharePoint connectivity and data discovery.
"""

import argparse
import logging
import sys
from pathlib import Path
import pandas as pd

from sharepoint_connector import SharePointConfig, SharePointConnector, SharePointAudioManager
from config import ConfigManager


def test_sharepoint_connection(sharepoint_config: SharePointConfig):
    """Test basic SharePoint connection."""
    print("Testing SharePoint connection...")
    
    try:
        connector = SharePointConnector(sharepoint_config)
        print("✓ Successfully connected to SharePoint")
        return connector
    except Exception as e:
        print(f"✗ SharePoint connection failed: {e}")
        return None


def test_participant_discovery(connector: SharePointConnector):
    """Test participant directory discovery."""
    print("\nTesting participant discovery...")
    
    try:
        participants = connector.list_participant_directories()
        print(f"✓ Found {len(participants)} participant directories")
        
        # Show first few participants
        if participants:
            print("Sample participants:")
            for participant in participants[:10]:
                print(f"  - {participant}")
            if len(participants) > 10:
                print(f"  ... and {len(participants) - 10} more")
        
        return participants
    except Exception as e:
        print(f"✗ Participant discovery failed: {e}")
        return []


def test_audio_file_discovery(connector: SharePointConnector, participants: list, max_test: int = 5):
    """Test audio file discovery for sample participants."""
    print(f"\nTesting audio file discovery for {min(max_test, len(participants))} participants...")
    
    audio_stats = {}
    
    for i, participant_id in enumerate(participants[:max_test]):
        print(f"Testing {participant_id}...")
        
        try:
            audio_files = connector.get_participant_audio_files(participant_id)
            audio_stats[participant_id] = len(audio_files)
            
            if audio_files:
                print(f"  ✓ Found {len(audio_files)} audio files")
                # Show first file as example
                print(f"    Example: {audio_files[0]}")
            else:
                print(f"  ⚠ No audio files found")
                
        except Exception as e:
            print(f"  ✗ Error: {e}")
            audio_stats[participant_id] = 0
    
    return audio_stats


def test_file_download(connector: SharePointConnector, participants: list, cache_dir: Path):
    """Test downloading a single file."""
    print(f"\nTesting file download...")
    
    # Find a participant with audio files
    for participant_id in participants[:5]:
        try:
            audio_files = connector.get_participant_audio_files(participant_id)
            if audio_files:
                # Try to download the first file
                sharepoint_path = audio_files[0]
                file_name = Path(sharepoint_path).name
                local_path = cache_dir / "test_download" / file_name
                
                print(f"Downloading {file_name} from {participant_id}...")
                
                if connector.download_file(sharepoint_path, local_path):
                    file_size = local_path.stat().st_size
                    print(f"✓ Successfully downloaded {file_name} ({file_size} bytes)")
                    
                    # Clean up test file
                    local_path.unlink()
                    if local_path.parent.exists() and not any(local_path.parent.iterdir()):
                        local_path.parent.rmdir()
                    
                    return True
                else:
                    print(f"✗ Failed to download {file_name}")
                    
        except Exception as e:
            print(f"✗ Download test failed: {e}")
    
    print("⚠ No files available for download test")
    return False


def compare_with_csv_participants(csv_path: Path, sharepoint_participants: list):
    """Compare SharePoint participants with CSV participants."""
    print(f"\nComparing with CSV participants...")
    
    try:
        df = pd.read_csv(csv_path)
        csv_participants = set(df['participant_id'].unique())
        sharepoint_set = set(sharepoint_participants)
        
        print(f"CSV participants: {len(csv_participants)}")
        print(f"SharePoint participants: {len(sharepoint_set)}")
        
        # Find overlaps
        common = csv_participants & sharepoint_set
        csv_only = csv_participants - sharepoint_set
        sharepoint_only = sharepoint_set - csv_participants
        
        print(f"Common participants: {len(common)}")
        print(f"CSV only: {len(csv_only)}")
        print(f"SharePoint only: {len(sharepoint_only)}")
        
        if csv_only:
            print(f"Participants in CSV but not SharePoint (first 10):")
            for p in list(csv_only)[:10]:
                print(f"  - {p}")
        
        if sharepoint_only:
            print(f"Participants in SharePoint but not CSV (first 10):")
            for p in list(sharepoint_only)[:10]:
                print(f"  - {p}")
        
        return {
            'csv_participants': len(csv_participants),
            'sharepoint_participants': len(sharepoint_set),
            'common': len(common),
            'csv_only': len(csv_only),
            'sharepoint_only': len(sharepoint_only)
        }
        
    except Exception as e:
        print(f"✗ Error comparing with CSV: {e}")
        return None


def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test SharePoint connectivity")
    
    parser.add_argument("--sharepoint-url", required=True, help="SharePoint site URL")
    parser.add_argument("--sharepoint-username", required=True, help="SharePoint username")
    parser.add_argument("--sharepoint-password", required=True, help="SharePoint password")
    parser.add_argument("--base-dir", type=Path, default=Path(__file__).parent, help="Base directory")
    parser.add_argument("--max-test-participants", type=int, default=5, help="Max participants to test")
    parser.add_argument("--test-download", action="store_true", help="Test file download")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="INFO")
    
    args = parser.parse_args()
    
    # Set up logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("="*60)
    print("SHAREPOINT CONNECTIVITY TEST")
    print("="*60)
    
    # Create SharePoint configuration
    sharepoint_config = SharePointConfig(
        sharepoint_url=args.sharepoint_url,
        username=args.sharepoint_username,
        password=args.sharepoint_password
    )
    
    # Test connection
    connector = test_sharepoint_connection(sharepoint_config)
    if not connector:
        sys.exit(1)
    
    # Test participant discovery
    participants = test_participant_discovery(connector)
    if not participants:
        print("No participants found. Check SharePoint path and permissions.")
        sys.exit(1)
    
    # Test audio file discovery
    audio_stats = test_audio_file_discovery(connector, participants, args.max_test_participants)
    
    # Test file download if requested
    if args.test_download:
        cache_dir = args.base_dir / "test_cache"
        cache_dir.mkdir(exist_ok=True)
        test_file_download(connector, participants, cache_dir)
    
    # Compare with CSV if available
    csv_path = args.base_dir / "participants_at_risk_groups.csv"
    if csv_path.exists():
        comparison = compare_with_csv_participants(csv_path, participants)
    else:
        print(f"\nCSV file not found at {csv_path}")
        print("Run the data processing pipeline first to generate the CSV")
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"SharePoint connection: ✓")
    print(f"Participants found: {len(participants)}")
    print(f"Participants with audio: {sum(1 for count in audio_stats.values() if count > 0)}")
    print(f"Total audio files tested: {sum(audio_stats.values())}")
    
    if args.test_download:
        print("File download: Tested")
    
    print("\nReady to run the full pipeline!")
    print("Use: python sharepoint_pipeline.py --sharepoint-url ... --sharepoint-username ... --sharepoint-password ...")


if __name__ == "__main__":
    main()
