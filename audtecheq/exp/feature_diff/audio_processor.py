"""
Audio processing module for embedding extraction and voice activity detection.
"""

import numpy as np
import torch
import torchaudio
import librosa
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union
import logging
import warnings

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)

try:
    from transformers import Wav2Vec2Processor, Wav2Vec2Model
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Warning: transformers library not available. Install with: pip install transformers")

from config import ConfigManager


class VoiceActivityDetector:
    """Voice Activity Detection using energy-based approach."""

    def __init__(self, threshold: float = 0.5, frame_length: int = 2048, hop_length: int = 512):
        """
        Initialize VAD.

        Args:
            threshold: Energy threshold for voice activity
            frame_length: Frame length for analysis
            hop_length: Hop length for analysis
        """
        self.threshold = threshold
        self.frame_length = frame_length
        self.hop_length = hop_length

        self.logger = logging.getLogger(__name__)

    def detect_voice_activity(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Detect voice activity in audio signal.

        Args:
            audio: Audio signal
            sample_rate: Sample rate

        Returns:
            Boolean array indicating voice activity
        """
        # Compute short-time energy
        energy = librosa.feature.rms(
            y=audio,
            frame_length=self.frame_length,
            hop_length=self.hop_length
        )[0]

        # Normalize energy
        energy_normalized = (energy - np.min(energy)) / (np.max(energy) - np.min(energy) + 1e-8)

        # Apply threshold
        voice_activity = energy_normalized > self.threshold

        # Convert frame-level decisions to sample-level
        voice_samples = np.repeat(voice_activity, self.hop_length)

        # Ensure same length as original audio
        if len(voice_samples) > len(audio):
            voice_samples = voice_samples[:len(audio)]
        elif len(voice_samples) < len(audio):
            voice_samples = np.pad(voice_samples, (0, len(audio) - len(voice_samples)))

        return voice_samples

    def apply_vad(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Apply VAD to remove silence from audio.

        Args:
            audio: Audio signal
            sample_rate: Sample rate

        Returns:
            Audio with silence removed
        """
        voice_activity = self.detect_voice_activity(audio, sample_rate)

        if np.sum(voice_activity) == 0:
            self.logger.warning("No voice activity detected, returning original audio")
            return audio

        return audio[voice_activity]


class AudioEmbeddingExtractor:
    """Extracts embeddings from audio using pretrained models."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the embedding extractor.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.model = None
        self.processor = None
        self.device = self._get_optimal_device()
        self.device_info = self._get_device_info()

        # Initialize VAD if enabled
        if self.config.audio_config.apply_vad:
            self.vad = VoiceActivityDetector(threshold=self.config.audio_config.vad_threshold)
        else:
            self.vad = None

        self.logger = logging.getLogger(__name__)
        self._log_device_info()
        self._load_model()

    def _get_optimal_device(self):
        """Get the optimal device for computation (GPU if available, otherwise CPU)."""
        import platform

        # Check for CUDA (NVIDIA GPUs) - Windows/Linux
        if torch.cuda.is_available():
            return torch.device("cuda")

        # Check for Metal Performance Shaders (Apple Silicon Macs)
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return torch.device("mps")

        # Fallback to CPU
        return torch.device("cpu")

    def _get_device_info(self):
        """Get detailed information about the selected device."""
        import platform

        device_info = {
            'device_type': str(self.device),
            'platform': platform.system(),
            'architecture': platform.machine(),
        }

        if self.device.type == "cuda":
            device_info.update({
                'gpu_name': torch.cuda.get_device_name(0),
                'gpu_memory': f"{torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB",
                'cuda_version': torch.version.cuda,
                'gpu_count': torch.cuda.device_count()
            })
        elif self.device.type == "mps":
            device_info.update({
                'gpu_name': 'Apple Silicon GPU',
                'backend': 'Metal Performance Shaders',
                'unified_memory': True
            })
        else:
            import psutil
            device_info.update({
                'cpu_count': psutil.cpu_count(),
                'memory_gb': f"{psutil.virtual_memory().total / 1e9:.1f} GB"
            })

        return device_info

    def _log_device_info(self):
        """Log device information for debugging and optimization."""
        self.logger.info(f"Using device: {self.device}")

        if self.device.type == "cuda":
            self.logger.info(f"GPU: {self.device_info['gpu_name']}")
            self.logger.info(f"GPU Memory: {self.device_info['gpu_memory']}")
            self.logger.info(f"CUDA Version: {self.device_info['cuda_version']}")
            if self.device_info['gpu_count'] > 1:
                self.logger.info(f"Multiple GPUs available: {self.device_info['gpu_count']}")
        elif self.device.type == "mps":
            self.logger.info(f"Using Apple Silicon GPU with Metal Performance Shaders")
            self.logger.info(f"Unified memory architecture detected")
        else:
            self.logger.info(f"Using CPU with {self.device_info['cpu_count']} cores")
            self.logger.info(f"System Memory: {self.device_info['memory_gb']}")

    def _optimize_for_device(self):
        """Apply device-specific optimizations."""
        if self.device.type == "cuda":
            # Enable CUDA optimizations
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            self.logger.info("Enabled CUDA optimizations")
        elif self.device.type == "mps":
            # MPS-specific optimizations
            # Note: MPS is relatively new, fewer optimization options available
            self.logger.info("Using MPS optimizations")
        else:
            # CPU optimizations
            torch.set_num_threads(min(8, torch.get_num_threads()))  # Limit threads to avoid oversubscription
            self.logger.info(f"Set CPU threads to {torch.get_num_threads()}")

    def _load_model(self):
        """Load the pretrained embedding model."""
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library is required for embedding extraction")

        try:
            model_name = self.config.audio_config.embedding_model
            self.processor = Wav2Vec2Processor.from_pretrained(model_name)
            self.model = Wav2Vec2Model.from_pretrained(model_name)

            # Move model to device
            self.model.to(self.device)
            self.model.eval()

            # Apply device-specific optimizations
            self._optimize_for_device()

            # Enable mixed precision for CUDA if available
            if self.device.type == "cuda":
                try:
                    # Enable automatic mixed precision for faster inference
                    self.use_amp = True
                    self.logger.info("Enabled automatic mixed precision (AMP)")
                except:
                    self.use_amp = False
            else:
                self.use_amp = False

            self.logger.info(f"Loaded model: {model_name} on {self.device}")

        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            raise

    def load_audio(self, audio_path: Path) -> Tuple[np.ndarray, int]:
        """
        Load audio file.

        Args:
            audio_path: Path to audio file

        Returns:
            Tuple of (audio_data, sample_rate)
        """
        try:
            # Load audio using librosa for consistent preprocessing
            audio, sr = librosa.load(
                audio_path,
                sr=self.config.audio_config.sample_rate,
                duration=self.config.audio_config.max_duration
            )

            return audio, sr

        except Exception as e:
            self.logger.error(f"Error loading audio {audio_path}: {e}")
            raise

    def preprocess_audio(self, audio: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Preprocess audio (apply VAD, normalization, etc.).

        Args:
            audio: Audio signal
            sample_rate: Sample rate

        Returns:
            Preprocessed audio
        """
        # Apply VAD if enabled
        if self.vad is not None:
            audio = self.vad.apply_vad(audio, sample_rate)

        # Normalize audio
        if np.max(np.abs(audio)) > 0:
            audio = audio / np.max(np.abs(audio))

        return audio

    def extract_embedding(self, audio: np.ndarray) -> np.ndarray:
        """
        Extract embedding from audio using the pretrained model.

        Args:
            audio: Preprocessed audio signal

        Returns:
            Embedding vector
        """
        try:
            # Process audio for model input
            inputs = self.processor(
                audio,
                sampling_rate=self.config.audio_config.sample_rate,
                return_tensors="pt",
                padding=True
            )

            # Move to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Extract features with optimized inference
            with torch.no_grad():
                if self.use_amp and self.device.type == "cuda":
                    # Use automatic mixed precision for CUDA
                    with torch.cuda.amp.autocast():
                        outputs = self.model(**inputs)
                else:
                    outputs = self.model(**inputs)

                # Get the last hidden state and average over time dimension
                last_hidden_state = outputs.last_hidden_state  # Shape: (batch, time, features)
                embedding = torch.mean(last_hidden_state, dim=1)  # Average over time

                # Convert to numpy
                embedding = embedding.cpu().numpy().squeeze()

            return embedding

        except Exception as e:
            self.logger.error(f"Error extracting embedding: {e}")
            raise

    def process_audio_file(self, audio_path: Path) -> Optional[np.ndarray]:
        """
        Process a single audio file to extract embedding.

        Args:
            audio_path: Path to audio file

        Returns:
            Embedding vector or None if processing failed
        """
        try:
            # Load audio
            audio, sample_rate = self.load_audio(audio_path)

            # Check if audio is too short
            if len(audio) < self.config.audio_config.sample_rate * 0.1:  # Less than 0.1 seconds
                self.logger.warning(f"Audio too short: {audio_path}")
                return None

            # Preprocess
            audio = self.preprocess_audio(audio, sample_rate)

            # Check if audio remains after preprocessing
            if len(audio) == 0:
                self.logger.warning(f"No audio remaining after preprocessing: {audio_path}")
                return None

            # Extract embedding
            embedding = self.extract_embedding(audio)

            return embedding

        except Exception as e:
            self.logger.error(f"Error processing {audio_path}: {e}")
            return None
        finally:
            # Clear GPU cache if using CUDA
            if self.device.type == "cuda":
                torch.cuda.empty_cache()

    def get_memory_usage(self):
        """Get current memory usage information."""
        memory_info = {}

        if self.device.type == "cuda":
            memory_info['gpu_allocated'] = f"{torch.cuda.memory_allocated() / 1e9:.2f} GB"
            memory_info['gpu_cached'] = f"{torch.cuda.memory_reserved() / 1e9:.2f} GB"
            memory_info['gpu_max_allocated'] = f"{torch.cuda.max_memory_allocated() / 1e9:.2f} GB"
        elif self.device.type == "mps":
            memory_info['mps_allocated'] = f"{torch.mps.current_allocated_memory() / 1e9:.2f} GB"
        else:
            import psutil
            memory_info['cpu_memory'] = f"{psutil.virtual_memory().percent}% used"

        return memory_info

    def clear_memory(self):
        """Clear device memory caches."""
        if self.device.type == "cuda":
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            self.logger.info("Cleared CUDA memory cache")
        elif self.device.type == "mps":
            torch.mps.empty_cache()
            self.logger.info("Cleared MPS memory cache")


class ParticipantEmbeddingAggregator:
    """Aggregates embeddings across multiple audio files per participant."""

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the aggregator.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.extractor = AudioEmbeddingExtractor(config_manager)
        self.logger = logging.getLogger(__name__)

    def aggregate_participant_embeddings(
        self,
        audio_files: List[Path],
        aggregation_method: str = "mean"
    ) -> Optional[np.ndarray]:
        """
        Aggregate embeddings from multiple audio files for a participant.

        Args:
            audio_files: List of audio file paths for the participant
            aggregation_method: Method for aggregation ("mean", "median", "max")

        Returns:
            Aggregated embedding vector or None if no valid embeddings
        """
        embeddings = []

        for audio_file in audio_files:
            embedding = self.extractor.process_audio_file(audio_file)
            if embedding is not None:
                embeddings.append(embedding)

        if not embeddings:
            self.logger.warning(f"No valid embeddings extracted from {len(audio_files)} files")
            return None

        embeddings = np.array(embeddings)

        # Aggregate embeddings
        if aggregation_method == "mean":
            aggregated = np.mean(embeddings, axis=0)
        elif aggregation_method == "median":
            aggregated = np.median(embeddings, axis=0)
        elif aggregation_method == "max":
            aggregated = np.max(embeddings, axis=0)
        else:
            raise ValueError(f"Unknown aggregation method: {aggregation_method}")

        self.logger.info(f"Aggregated {len(embeddings)} embeddings using {aggregation_method}")

        return aggregated

    def process_all_participants(
        self,
        participant_audio_files: Dict[str, List[Path]]
    ) -> Dict[str, np.ndarray]:
        """
        Process all participants to extract aggregated embeddings.

        Args:
            participant_audio_files: Dictionary mapping participant IDs to audio file lists

        Returns:
            Dictionary mapping participant IDs to aggregated embeddings
        """
        participant_embeddings = {}

        total_participants = len(participant_audio_files)

        for i, (participant_id, audio_files) in enumerate(participant_audio_files.items(), 1):
            self.logger.info(f"Processing participant {participant_id} ({i}/{total_participants})")

            # Log memory usage periodically
            if i % 10 == 0:
                memory_info = self.extractor.get_memory_usage()
                self.logger.info(f"Memory usage after {i} participants: {memory_info}")

            aggregated_embedding = self.aggregate_participant_embeddings(audio_files)

            if aggregated_embedding is not None:
                participant_embeddings[participant_id] = aggregated_embedding
            else:
                self.logger.warning(f"Failed to extract embedding for participant {participant_id}")

            # Clear memory cache periodically to prevent OOM
            if i % 20 == 0:
                self.extractor.clear_memory()

        # Final memory cleanup
        self.extractor.clear_memory()
        self.logger.info(f"Successfully processed {len(participant_embeddings)} participants")

        return participant_embeddings

    def save_embeddings(
        self,
        participant_embeddings: Dict[str, np.ndarray],
        output_path: Optional[Path] = None
    ):
        """
        Save participant embeddings to file.

        Args:
            participant_embeddings: Dictionary of participant embeddings
            output_path: Optional custom output path
        """
        if output_path is None:
            output_path = self.config.path_config.output_dir / "participant_embeddings.npz"

        # Convert to format suitable for saving
        participant_ids = list(participant_embeddings.keys())
        embeddings_array = np.array(list(participant_embeddings.values()))

        np.savez(
            output_path,
            participant_ids=participant_ids,
            embeddings=embeddings_array
        )

        self.logger.info(f"Saved embeddings for {len(participant_ids)} participants to {output_path}")

    def load_embeddings(self, input_path: Path) -> Dict[str, np.ndarray]:
        """
        Load participant embeddings from file.

        Args:
            input_path: Path to embeddings file

        Returns:
            Dictionary mapping participant IDs to embeddings
        """
        data = np.load(input_path)
        participant_ids = data['participant_ids']
        embeddings = data['embeddings']

        participant_embeddings = {
            pid: emb for pid, emb in zip(participant_ids, embeddings)
        }

        self.logger.info(f"Loaded embeddings for {len(participant_embeddings)} participants")

        return participant_embeddings
