#!/usr/bin/env python3
"""
GPU detection and optimization test script.
Tests hardware detection and performance optimization features.
"""

import sys
import time
import platform
from pathlib import Path
import numpy as np

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available. Install with: pip install torch")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("psutil not available. Install with: pip install psutil")


def test_hardware_detection():
    """Test hardware detection capabilities."""
    print("="*60)
    print("HARDWARE DETECTION TEST")
    print("="*60)
    
    if not TORCH_AVAILABLE:
        print("❌ PyTorch not available - cannot test GPU detection")
        return False
    
    print(f"Platform: {platform.system()} {platform.machine()}")
    print(f"PyTorch version: {torch.__version__}")
    
    # Test CUDA
    cuda_available = torch.cuda.is_available()
    print(f"CUDA available: {'✅' if cuda_available else '❌'}")
    
    if cuda_available:
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"  GPU {i}: {props.name}")
            print(f"    Memory: {props.total_memory / 1e9:.1f} GB")
            print(f"    Compute capability: {props.major}.{props.minor}")
    
    # Test MPS (Apple Silicon)
    mps_available = hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()
    print(f"MPS (Apple Silicon) available: {'✅' if mps_available else '❌'}")
    
    # Determine optimal device
    if cuda_available:
        device = torch.device("cuda")
        device_name = "NVIDIA GPU (CUDA)"
    elif mps_available:
        device = torch.device("mps")
        device_name = "Apple Silicon GPU (MPS)"
    else:
        device = torch.device("cpu")
        device_name = "CPU"
    
    print(f"\n🎯 Optimal device: {device} ({device_name})")
    
    return True


def test_device_performance():
    """Test performance on different devices."""
    print("\n" + "="*60)
    print("DEVICE PERFORMANCE TEST")
    print("="*60)
    
    if not TORCH_AVAILABLE:
        print("❌ PyTorch not available - cannot test performance")
        return
    
    # Test matrix operations on different devices
    devices_to_test = []
    
    if torch.cuda.is_available():
        devices_to_test.append(("cuda", "NVIDIA GPU"))
    
    if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        devices_to_test.append(("mps", "Apple Silicon GPU"))
    
    devices_to_test.append(("cpu", "CPU"))
    
    # Test parameters
    matrix_size = 1000
    iterations = 10
    
    print(f"Testing matrix multiplication ({matrix_size}x{matrix_size}, {iterations} iterations)")
    print()
    
    results = {}
    
    for device_name, device_desc in devices_to_test:
        print(f"Testing {device_desc} ({device_name})...")
        
        try:
            device = torch.device(device_name)
            
            # Create test matrices
            a = torch.randn(matrix_size, matrix_size, device=device)
            b = torch.randn(matrix_size, matrix_size, device=device)
            
            # Warmup
            for _ in range(3):
                _ = torch.mm(a, b)
            
            if device_name == "cuda":
                torch.cuda.synchronize()
            elif device_name == "mps":
                torch.mps.synchronize()
            
            # Benchmark
            start_time = time.time()
            
            for _ in range(iterations):
                result = torch.mm(a, b)
            
            if device_name == "cuda":
                torch.cuda.synchronize()
            elif device_name == "mps":
                torch.mps.synchronize()
            
            end_time = time.time()
            
            avg_time = (end_time - start_time) / iterations
            results[device_name] = avg_time
            
            print(f"  ✅ Average time: {avg_time:.4f} seconds")
            
            # Memory usage
            if device_name == "cuda":
                memory_used = torch.cuda.memory_allocated() / 1e9
                print(f"  📊 GPU memory used: {memory_used:.2f} GB")
            elif device_name == "mps":
                try:
                    memory_used = torch.mps.current_allocated_memory() / 1e9
                    print(f"  📊 MPS memory used: {memory_used:.2f} GB")
                except:
                    print("  📊 MPS memory info not available")
            
        except Exception as e:
            print(f"  ❌ Error testing {device_desc}: {e}")
    
    # Compare performance
    if len(results) > 1:
        print("\n📈 Performance Comparison:")
        cpu_time = results.get("cpu", 1.0)
        
        for device_name, avg_time in sorted(results.items(), key=lambda x: x[1]):
            speedup = cpu_time / avg_time if avg_time > 0 else 1.0
            print(f"  {device_name}: {speedup:.1f}x faster than CPU")


def test_memory_monitoring():
    """Test memory monitoring capabilities."""
    print("\n" + "="*60)
    print("MEMORY MONITORING TEST")
    print("="*60)
    
    if PSUTIL_AVAILABLE:
        # System memory
        memory = psutil.virtual_memory()
        print(f"System Memory:")
        print(f"  Total: {memory.total / 1e9:.1f} GB")
        print(f"  Available: {memory.available / 1e9:.1f} GB")
        print(f"  Used: {memory.percent:.1f}%")
        
        # CPU info
        print(f"\nCPU:")
        print(f"  Cores: {psutil.cpu_count()}")
        print(f"  Usage: {psutil.cpu_percent(interval=1):.1f}%")
    
    if TORCH_AVAILABLE:
        # GPU memory
        if torch.cuda.is_available():
            print(f"\nCUDA Memory:")
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                allocated = torch.cuda.memory_allocated(i) / 1e9
                reserved = torch.cuda.memory_reserved(i) / 1e9
                total = props.total_memory / 1e9
                
                print(f"  GPU {i} ({props.name}):")
                print(f"    Total: {total:.1f} GB")
                print(f"    Allocated: {allocated:.2f} GB")
                print(f"    Reserved: {reserved:.2f} GB")
                print(f"    Free: {total - reserved:.1f} GB")
        
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print(f"\nMPS Memory:")
            try:
                allocated = torch.mps.current_allocated_memory() / 1e9
                print(f"  Allocated: {allocated:.2f} GB")
            except:
                print("  Memory info not available")


def test_audio_processor_gpu():
    """Test the audio processor GPU detection."""
    print("\n" + "="*60)
    print("AUDIO PROCESSOR GPU TEST")
    print("="*60)
    
    try:
        from config import ConfigManager
        from audio_processor import AudioEmbeddingExtractor
        
        # Initialize config
        config = ConfigManager()
        config.set_base_directory(Path(__file__).parent)
        
        print("Initializing AudioEmbeddingExtractor...")
        
        # This will test the GPU detection in our audio processor
        try:
            extractor = AudioEmbeddingExtractor(config)
            print("✅ AudioEmbeddingExtractor initialized successfully")
            
            # Test memory monitoring
            memory_info = extractor.get_memory_usage()
            print(f"📊 Memory usage: {memory_info}")
            
            # Test memory clearing
            extractor.clear_memory()
            print("✅ Memory clearing test completed")
            
        except ImportError as e:
            print(f"⚠️  Cannot test audio processor: {e}")
            print("Install missing dependencies: pip install transformers")
        
    except ImportError:
        print("⚠️  Audio processor modules not available")


def print_installation_recommendations():
    """Print installation recommendations based on detected hardware."""
    print("\n" + "="*60)
    print("INSTALLATION RECOMMENDATIONS")
    print("="*60)
    
    system = platform.system()
    machine = platform.machine()
    
    print(f"Detected system: {system} {machine}")
    
    if TORCH_AVAILABLE:
        if torch.cuda.is_available():
            print("\n🎯 NVIDIA GPU detected - Recommended installation:")
            print("pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118")
            print("pip install -r requirements.txt")
            
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("\n🎯 Apple Silicon detected - Recommended installation:")
            print("pip install torch torchaudio")
            print("pip install -r requirements.txt")
            
        else:
            print("\n🎯 CPU-only detected - Recommended installation:")
            print("pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu")
            print("pip install -r requirements.txt")
    else:
        print("\n🎯 PyTorch not installed - Install based on your hardware:")
        
        if system == "Darwin" and machine == "arm64":
            print("# For Apple Silicon Mac:")
            print("pip install torch torchaudio")
        elif system in ["Windows", "Linux"]:
            print("# For NVIDIA GPU:")
            print("pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118")
            print("# For CPU only:")
            print("pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu")
        
        print("pip install -r requirements.txt")


def main():
    """Run all GPU tests."""
    print("GPU DETECTION AND OPTIMIZATION TESTS")
    print("This script tests hardware detection and performance optimization")
    print()
    
    # Run tests
    hardware_ok = test_hardware_detection()
    
    if hardware_ok:
        test_device_performance()
        test_memory_monitoring()
        test_audio_processor_gpu()
    
    print_installation_recommendations()
    
    print("\n" + "="*60)
    print("GPU TESTS COMPLETED")
    print("="*60)
    
    if TORCH_AVAILABLE:
        device = "cuda" if torch.cuda.is_available() else "mps" if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available() else "cpu"
        print(f"🎯 Your system will use: {device}")
        
        if device == "cuda":
            print("🚀 NVIDIA GPU acceleration available - expect 5-15x speedup")
        elif device == "mps":
            print("🚀 Apple Silicon acceleration available - expect 3-8x speedup")
        else:
            print("💻 CPU processing - reliable but slower")
    else:
        print("⚠️  Install PyTorch to enable GPU acceleration")


if __name__ == "__main__":
    main()
