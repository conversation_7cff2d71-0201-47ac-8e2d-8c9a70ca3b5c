# Checkpoint and Resumption Guide

This guide explains how to use the robust checkpointing system in the SharePoint audio classification pipeline.

## Overview

The pipeline now includes comprehensive checkpointing that saves progress at each major step, allowing you to:
- **Resume from interruptions** without losing progress
- **Skip completed steps** when rerunning
- **Manage large datasets** efficiently
- **Debug specific steps** by clearing and rerunning them
- **Save time and resources** by avoiding recomputation

## Checkpoint Structure

### Checkpoint Files
```
audtecheq/exp/feature_diff/checkpoints/
├── pipeline_state.json              # Overall pipeline state
├── participant_metadata.pkl         # Processed participant data
├── audio_files_mapping.pkl          # Audio file discovery results
├── participant_embeddings.npz       # Extracted embeddings (largest file)
├── participant_labels.pkl           # At-risk labels
├── trained_model.pkl               # Trained ML model
└── training_results.json           # Training metrics and results
```

### Pipeline Steps
1. **`load_metadata`** - Load and process participant metadata
2. **`discover_audio`** - Download audio files from SharePoint
3. **`extract_embeddings`** - Extract Wav2Vec2 embeddings
4. **`train_model`** - Train and evaluate ML models

## Usage Examples

### 1. Basic Run with Checkpointing (Recommended)
```bash
# First run - will create checkpoints
python sharepoint_pipeline.py \
  --sharepoint-url "https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/" \
  --sharepoint-username "<EMAIL>" \
  --sharepoint-password "Megalophiya*092426" \
  --max-participants 20

# If interrupted, resume from last checkpoint
python sharepoint_pipeline.py \
  --sharepoint-url "https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/" \
  --sharepoint-username "<EMAIL>" \
  --sharepoint-password "Megalophiya*092426" \
  --max-participants 20
  # Will automatically resume from where it left off
```

### 2. Check Checkpoint Status
```bash
# Show current checkpoint status
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --show-checkpoints
```

### 3. Force Recomputation
```bash
# Force recompute embeddings only
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --force-recompute-embeddings

# Force recompute everything
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --force-recompute-all
```

### 4. Clear Specific Checkpoints
```bash
# Clear all checkpoints and start fresh
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --clear-checkpoints all

# Clear only embeddings checkpoint
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --clear-checkpoints embeddings

# Available options: all, metadata, audio, embeddings, model
```

### 5. Start Fresh (No Resumption)
```bash
# Don't resume from checkpoints, start completely fresh
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --no-resume
```

### 6. Custom Checkpoint Directory
```bash
# Use custom checkpoint directory
python sharepoint_pipeline.py \
  --sharepoint-url "..." \
  --sharepoint-username "..." \
  --sharepoint-password "..." \
  --checkpoint-dir /path/to/custom/checkpoints
```

## Python API Usage

### Basic Checkpointing
```python
from sharepoint_pipeline import SharePointAudioPipeline
from sharepoint_connector import SharePointConfig

# Initialize with checkpointing
pipeline = SharePointAudioPipeline(
    base_dir=Path("data"),
    sharepoint_config=sharepoint_config,
    checkpoint_dir=Path("custom_checkpoints")  # Optional
)

# Run with automatic resumption
results = pipeline.run_sharepoint_pipeline(
    max_participants=50,
    resume_from_checkpoint=True  # Default
)
```

### Manual Checkpoint Management
```python
# Check checkpoint status
pipeline.checkpoint_manager.print_checkpoint_status()

# Check if specific step is completed
if pipeline.checkpoint_manager.is_step_completed("extract_embeddings"):
    print("Embeddings already extracted")

# Clear specific checkpoint
pipeline.checkpoint_manager.clear_checkpoints("embeddings")

# Get checkpoint summary
summary = pipeline.checkpoint_manager.get_checkpoint_summary()
print(f"Completed steps: {summary['state']['completed_steps']}")
```

## Best Practices

### 1. Start Small, Scale Up
```bash
# First run: Test with small dataset
python sharepoint_pipeline.py ... --max-participants 5

# If successful, scale up
python sharepoint_pipeline.py ... --max-participants 20

# Finally, run full dataset
python sharepoint_pipeline.py ... # No limit
```

### 2. Monitor Progress
```bash
# Check status before running
python sharepoint_pipeline.py ... --show-checkpoints

# Run pipeline
python sharepoint_pipeline.py ...

# Check status after interruption
python sharepoint_pipeline.py ... --show-checkpoints
```

### 3. Handle Configuration Changes
```bash
# If you change audio processing settings, clear embeddings
python sharepoint_pipeline.py ... --clear-checkpoints embeddings

# If you change model settings, clear model checkpoint
python sharepoint_pipeline.py ... --clear-checkpoints model
```

### 4. Debugging Specific Steps
```bash
# Debug embedding extraction
python sharepoint_pipeline.py ... --clear-checkpoints embeddings --max-participants 3

# Debug model training
python sharepoint_pipeline.py ... --clear-checkpoints model
```

## Checkpoint File Sizes

Typical file sizes for different participant counts:

| Participants | Metadata | Audio Mapping | Embeddings | Model | Total |
|-------------|----------|---------------|------------|-------|-------|
| 10          | 1 KB     | 5 KB          | 2 MB       | 1 MB  | 3 MB  |
| 50          | 5 KB     | 25 KB         | 10 MB      | 5 MB  | 15 MB |
| 200         | 20 KB    | 100 KB        | 40 MB      | 20 MB | 60 MB |

**Note**: Embeddings are the largest files (768 dimensions × participants)

## Recovery Scenarios

### 1. Pipeline Interrupted During Embedding Extraction
```bash
# Pipeline will automatically resume from audio download step
python sharepoint_pipeline.py ... 
# Output: "Step 'discover_audio' already completed, loading from checkpoint..."
# Output: "Running step 'extract_embeddings'..."
```

### 2. SharePoint Connection Issues
```bash
# Audio files already downloaded, will skip download step
python sharepoint_pipeline.py ...
# Output: "Step 'discover_audio' already completed, loading from checkpoint..."
```

### 3. Model Training Failure
```bash
# Clear only model checkpoint and retry
python sharepoint_pipeline.py ... --clear-checkpoints model
```

### 4. Configuration Changes
```bash
# Changed embedding model? Clear embeddings and model
python sharepoint_pipeline.py ... --clear-checkpoints embeddings
python sharepoint_pipeline.py ... --clear-checkpoints model
```

## Troubleshooting

### Checkpoint Corruption
```bash
# If checkpoints seem corrupted, start fresh
python sharepoint_pipeline.py ... --clear-checkpoints all
```

### Disk Space Issues
```bash
# Check checkpoint sizes
python sharepoint_pipeline.py ... --show-checkpoints

# Clear large embedding files if needed
python sharepoint_pipeline.py ... --clear-checkpoints embeddings
```

### Configuration Conflicts
```bash
# Pipeline detects config changes and will recompute affected steps
# Or manually clear affected checkpoints
python sharepoint_pipeline.py ... --clear-checkpoints embeddings
```

## Advanced Features

### Custom Checkpoint Logic
```python
# Check if recomputation is needed
should_recompute = pipeline.checkpoint_manager.should_recompute_step(
    "extract_embeddings", 
    force_recompute=False
)

# Manually mark step as completed
pipeline.checkpoint_manager.mark_step_completed(
    "custom_step", 
    metadata={"custom_info": "value"}
)
```

### Checkpoint Metadata
```python
# Get detailed step information
summary = pipeline.checkpoint_manager.get_checkpoint_summary()
step_metadata = summary['state']['step_metadata']

print(f"Embeddings extracted for {step_metadata['extract_embeddings']['participant_count']} participants")
print(f"Model accuracy: {step_metadata['train_model']['test_accuracy']}")
```

## Summary

The checkpointing system makes the pipeline robust and efficient:
- ✅ **Automatic resumption** from interruptions
- ✅ **Intelligent step skipping** for completed work
- ✅ **Flexible recomputation** options
- ✅ **Configuration change detection**
- ✅ **Detailed progress tracking**
- ✅ **Easy debugging** and troubleshooting

This allows you to run large-scale experiments confidently, knowing that progress is always saved and recoverable.
