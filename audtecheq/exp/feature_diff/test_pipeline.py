#!/usr/bin/env python3
"""
Test script for the audio at-risk classification pipeline.
This script demonstrates how to use the pipeline and tests basic functionality.
"""

import sys
import logging
from pathlib import Path
import numpy as np

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from config import ConfigManager
from data_loader import ParticipantDataLoader, AudioFileManager
from main_pipeline import AudioAtRiskPipeline


def test_config_manager():
    """Test configuration management."""
    print("Testing ConfigManager...")
    
    # Test with default config
    config = ConfigManager()
    config.set_base_directory(Path(__file__).parent)
    
    try:
        config.validate()
        print("✓ Configuration validation passed")
    except ValueError as e:
        print(f"✗ Configuration validation failed: {e}")
    
    # Test saving and loading config
    config_file = Path(__file__).parent / "test_config.json"
    config.save_to_file(config_file)
    
    # Load config
    config2 = ConfigManager(config_file)
    config2.set_base_directory(Path(__file__).parent)
    
    print("✓ Configuration save/load test passed")
    
    # Clean up
    if config_file.exists():
        config_file.unlink()


def test_data_loader():
    """Test data loading functionality."""
    print("\nTesting ParticipantDataLoader...")
    
    config = ConfigManager()
    config.set_base_directory(Path(__file__).parent)
    
    data_loader = ParticipantDataLoader(config)
    
    try:
        # Test loading participant metadata
        df = data_loader.load_participant_metadata()
        print(f"✓ Loaded {len(df)} participants")
        
        # Test loading field mappings
        mappings = data_loader.load_field_mappings()
        print(f"✓ Loaded field mappings for {len(mappings)} fields")
        
        # Test creating at-risk groups
        at_risk_df = data_loader.create_at_risk_groups()
        print(f"✓ Created at-risk groups for {len(at_risk_df)} participants")
        
        # Test getting classification data
        participant_ids, labels = data_loader.get_classification_data()
        print(f"✓ Got classification data for {len(participant_ids)} participants")
        
        # Test label distribution
        label_dist = data_loader.get_label_distribution()
        print(f"✓ Label distribution: {len(label_dist)} categories")
        
    except Exception as e:
        print(f"✗ Data loader test failed: {e}")


def test_audio_manager():
    """Test audio file management."""
    print("\nTesting AudioFileManager...")
    
    config = ConfigManager()
    config.set_base_directory(Path(__file__).parent)
    
    audio_manager = AudioFileManager(config)
    
    try:
        # Test audio file discovery
        audio_files = audio_manager.discover_audio_files()
        
        if audio_files:
            print(f"✓ Found audio files for {len(audio_files)} participants")
            
            # Show some examples
            for i, (participant_id, files) in enumerate(list(audio_files.items())[:3]):
                print(f"  {participant_id}: {len(files)} files")
        else:
            print("⚠ No audio files found (this is expected if no audio directory exists)")
        
        # Test getting participants with audio
        participants_with_audio = audio_manager.get_participants_with_audio()
        print(f"✓ {len(participants_with_audio)} participants have audio files")
        
    except Exception as e:
        print(f"✗ Audio manager test failed: {e}")


def test_pipeline_initialization():
    """Test pipeline initialization."""
    print("\nTesting Pipeline Initialization...")
    
    try:
        # Test with default config
        pipeline = AudioAtRiskPipeline(
            base_dir=Path(__file__).parent,
            config_file=None
        )
        print("✓ Pipeline initialized with default config")
        
        # Test with custom config file
        config_file = Path(__file__).parent / "pipeline_config.json"
        if config_file.exists():
            pipeline2 = AudioAtRiskPipeline(
                base_dir=Path(__file__).parent,
                config_file=config_file
            )
            print("✓ Pipeline initialized with custom config")
        
    except Exception as e:
        print(f"✗ Pipeline initialization failed: {e}")


def test_data_loading_pipeline():
    """Test the data loading part of the pipeline."""
    print("\nTesting Data Loading Pipeline...")
    
    try:
        pipeline = AudioAtRiskPipeline(
            base_dir=Path(__file__).parent,
            config_file=None
        )
        
        # Test participant data loading
        participant_labels = pipeline.load_participant_data()
        print(f"✓ Loaded labels for {len(participant_labels)} participants")
        
        # Test audio file discovery
        audio_files = pipeline.discover_audio_files()
        if audio_files:
            print(f"✓ Found audio files for {len(audio_files)} participants")
        else:
            print("⚠ No audio files found")
        
    except Exception as e:
        print(f"✗ Data loading pipeline test failed: {e}")


def create_dummy_audio_files():
    """Create dummy audio files for testing (if needed)."""
    print("\nCreating dummy audio files for testing...")
    
    audio_dir = Path(__file__).parent / "audio"
    audio_dir.mkdir(exist_ok=True)
    
    # Create some dummy .wav files
    dummy_participants = ["CHI001", "CHI002", "ATL001", "ATL002"]
    
    for participant in dummy_participants:
        for i in range(2):  # 2 files per participant
            dummy_file = audio_dir / f"{participant}_recording{i+1}.wav"
            if not dummy_file.exists():
                # Create a minimal dummy file (just touch it)
                dummy_file.touch()
    
    print(f"✓ Created dummy audio files in {audio_dir}")
    return audio_dir


def cleanup_dummy_files(audio_dir):
    """Clean up dummy audio files."""
    if audio_dir.exists():
        for file in audio_dir.glob("*.wav"):
            file.unlink()
        audio_dir.rmdir()
        print("✓ Cleaned up dummy audio files")


def run_all_tests():
    """Run all tests."""
    print("="*60)
    print("AUDIO AT-RISK CLASSIFICATION PIPELINE TESTS")
    print("="*60)
    
    # Set up logging to reduce noise during testing
    logging.getLogger().setLevel(logging.WARNING)
    
    # Run tests
    test_config_manager()
    test_data_loader()
    test_audio_manager()
    test_pipeline_initialization()
    test_data_loading_pipeline()
    
    print("\n" + "="*60)
    print("TESTS COMPLETED")
    print("="*60)
    
    print("\nTo run the full pipeline with real audio files:")
    print("1. Place audio files in the 'audio' subdirectory")
    print("2. Ensure filenames contain participant IDs (e.g., CHI001_recording.wav)")
    print("3. Install required dependencies: pip install -r requirements.txt")
    print("4. Run: python main_pipeline.py")


if __name__ == "__main__":
    run_all_tests()
