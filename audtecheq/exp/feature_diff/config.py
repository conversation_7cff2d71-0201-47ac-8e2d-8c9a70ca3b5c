"""
Configuration module for audio processing and classification pipeline.
"""

import json
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class AudioConfig:
    """Configuration for audio processing parameters."""
    sample_rate: int = 16000
    max_duration: Optional[float] = None  # Maximum duration in seconds, None for no limit
    apply_vad: bool = True
    vad_threshold: float = 0.5
    embedding_model: str = "facebook/wav2vec2-base-960h"
    

@dataclass
class ModelConfig:
    """Configuration for machine learning models."""
    classifier_type: str = "xgboost"  # Options: "xgboost", "random_forest", "logistic_regression"
    test_size: float = 0.2
    random_state: int = 42
    cross_validation_folds: int = 5
    
    # XGBoost specific parameters
    xgb_params: Dict[str, Any] = None
    
    # Random Forest specific parameters
    rf_params: Dict[str, Any] = None
    
    # Logistic Regression specific parameters
    lr_params: Dict[str, Any] = None
    
    def __post_init__(self):
        """Set default parameters for each model type."""
        if self.xgb_params is None:
            self.xgb_params = {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': self.random_state
            }
        
        if self.rf_params is None:
            self.rf_params = {
                'n_estimators': 100,
                'max_depth': 10,
                'random_state': self.random_state
            }
        
        if self.lr_params is None:
            self.lr_params = {
                'random_state': self.random_state,
                'max_iter': 1000
            }


@dataclass
class PathConfig:
    """Configuration for file paths."""
    data_dir: Path
    audio_dir: Path
    output_dir: Path
    participants_tsv: Path
    participants_json: Path
    
    @classmethod
    def from_base_dir(cls, base_dir: Path):
        """Create PathConfig from base directory."""
        return cls(
            data_dir=base_dir,
            audio_dir=base_dir / "audio",  # Assuming audio files are in audio subdirectory
            output_dir=base_dir,
            participants_tsv=base_dir / "participants.tsv",
            participants_json=base_dir / "participants.json"
        )


class ConfigManager:
    """Manages configuration loading and validation."""
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Optional path to JSON configuration file
        """
        self.audio_config = AudioConfig()
        self.model_config = ModelConfig()
        self.path_config = None
        
        if config_file and config_file.exists():
            self.load_from_file(config_file)
    
    def load_from_file(self, config_file: Path):
        """Load configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            # Update audio config
            if 'audio' in config_data:
                for key, value in config_data['audio'].items():
                    if hasattr(self.audio_config, key):
                        setattr(self.audio_config, key, value)
            
            # Update model config
            if 'model' in config_data:
                for key, value in config_data['model'].items():
                    if hasattr(self.model_config, key):
                        setattr(self.model_config, key, value)
            
            print(f"Configuration loaded from {config_file}")
            
        except Exception as e:
            print(f"Error loading configuration: {e}")
            print("Using default configuration")
    
    def set_base_directory(self, base_dir: Path):
        """Set the base directory for paths."""
        self.path_config = PathConfig.from_base_dir(base_dir)
    
    def save_to_file(self, config_file: Path):
        """Save current configuration to JSON file."""
        config_data = {
            'audio': {
                'sample_rate': self.audio_config.sample_rate,
                'max_duration': self.audio_config.max_duration,
                'apply_vad': self.audio_config.apply_vad,
                'vad_threshold': self.audio_config.vad_threshold,
                'embedding_model': self.audio_config.embedding_model
            },
            'model': {
                'classifier_type': self.model_config.classifier_type,
                'test_size': self.model_config.test_size,
                'random_state': self.model_config.random_state,
                'cross_validation_folds': self.model_config.cross_validation_folds,
                'xgb_params': self.model_config.xgb_params,
                'rf_params': self.model_config.rf_params,
                'lr_params': self.model_config.lr_params
            }
        }
        
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"Configuration saved to {config_file}")
    
    def validate(self):
        """Validate configuration settings."""
        errors = []
        
        # Validate paths
        if self.path_config is None:
            errors.append("Base directory not set")
        else:
            if not self.path_config.participants_tsv.exists():
                errors.append(f"Participants TSV file not found: {self.path_config.participants_tsv}")
            
            if not self.path_config.participants_json.exists():
                errors.append(f"Participants JSON file not found: {self.path_config.participants_json}")
        
        # Validate audio config
        if self.audio_config.sample_rate <= 0:
            errors.append("Sample rate must be positive")
        
        if self.audio_config.vad_threshold < 0 or self.audio_config.vad_threshold > 1:
            errors.append("VAD threshold must be between 0 and 1")
        
        # Validate model config
        if self.model_config.test_size <= 0 or self.model_config.test_size >= 1:
            errors.append("Test size must be between 0 and 1")
        
        if self.model_config.cross_validation_folds < 2:
            errors.append("Cross validation folds must be at least 2")
        
        if errors:
            raise ValueError("Configuration validation failed:\n" + "\n".join(errors))
        
        return True
