#!/usr/bin/env python3
"""
Demo script that creates mock audio data and runs the complete pipeline
to demonstrate all functionality including GPU acceleration and checkpointing.
"""

import numpy as np
import soundfile as sf
from pathlib import Path
import pandas as pd
import logging

from main_pipeline import AudioAtRiskPipeline


def create_mock_audio_files(participants: list, audio_dir: Path, duration: float = 2.0, sample_rate: int = 16000):
    """Create mock audio files for testing."""
    audio_dir.mkdir(exist_ok=True)

    print(f"Creating mock audio files for {len(participants)} participants...")

    for participant_id in participants:
        # Create 2-3 audio files per participant
        num_files = np.random.randint(2, 4)

        for i in range(num_files):
            # Generate mock audio (white noise with some structure)
            samples = int(duration * sample_rate)

            # Create some structured noise that resembles speech patterns
            t = np.linspace(0, duration, samples)

            # Base frequency modulation (like speech formants)
            f1 = 200 + 100 * np.sin(2 * np.pi * 2 * t)  # First formant
            f2 = 800 + 200 * np.sin(2 * np.pi * 3 * t)  # Second formant

            # Generate audio with formant-like structure
            audio = (0.3 * np.sin(2 * np.pi * f1 * t) +
                    0.2 * np.sin(2 * np.pi * f2 * t) +
                    0.1 * np.random.randn(samples))  # Add noise

            # Apply envelope to make it more speech-like
            envelope = np.exp(-t / (duration * 0.8))  # Decay envelope
            audio = audio * envelope

            # Normalize
            audio = audio / np.max(np.abs(audio)) * 0.8

            # Save audio file
            filename = f"{participant_id}_recording_{i+1}.wav"
            filepath = audio_dir / filename

            sf.write(filepath, audio, sample_rate)

    total_files = sum(len(list(audio_dir.glob(f"{p}_*.wav"))) for p in participants)
    print(f"✓ Created {total_files} mock audio files")

    return total_files


def run_demo_pipeline():
    """Run the complete pipeline demo."""
    print("="*60)
    print("AUDIO AT-RISK CLASSIFICATION PIPELINE DEMO")
    print("="*60)

    # Set up logging
    logging.basicConfig(level=logging.INFO)

    # Get some participants from our CSV
    csv_path = Path("participants_at_risk_groups.csv")
    if not csv_path.exists():
        print("❌ participants_at_risk_groups.csv not found. Run the data processing first.")
        return

    df = pd.read_csv(csv_path)

    # Get participants with valid labels
    valid_participants = df[
        (df['at_risk_code'] != 'N/A') &
        (df['at_risk_group'] != 'Missing data')
    ]['participant_id'].tolist()

    # Select a diverse subset for demo (different at-risk groups)
    demo_participants = []

    # Get participants from different at-risk groups
    for group in df['at_risk_group'].unique():
        if group not in ['Missing data', 'Could not be determined (missing data)']:
            group_participants = df[df['at_risk_group'] == group]['participant_id'].tolist()
            # Take 2-3 from each group
            demo_participants.extend(group_participants[:3])

    # Limit to 12 participants total
    demo_participants = demo_participants[:12]

    print(f"Demo participants: {demo_participants}")
    print(f"At-risk distribution in demo:")
    demo_df = df[df['participant_id'].isin(demo_participants)]
    for group, count in demo_df['at_risk_group'].value_counts().items():
        print(f"  {group}: {count}")

    # Create mock audio files
    audio_dir = Path("audio")
    total_files = create_mock_audio_files(demo_participants, audio_dir)

    # Initialize pipeline
    print(f"\nInitializing pipeline...")
    pipeline = AudioAtRiskPipeline(base_dir=Path("."))

    # Run the complete pipeline
    print(f"\nRunning complete pipeline with {len(demo_participants)} participants...")

    try:
        results = pipeline.run_full_pipeline(
            force_recompute_embeddings=False,  # Use cache if available
            compare_models=True  # Compare different models
        )

        print("\n" + "="*60)
        print("DEMO PIPELINE COMPLETED SUCCESSFULLY!")
        print("="*60)

        if results:
            training_results = results.get('training_results', {})
            if training_results:
                print(f"✅ Model trained successfully")
                print(f"✅ Test accuracy: {training_results.get('test_accuracy', 0):.3f}")
                print(f"✅ CV accuracy: {training_results.get('cv_mean', 0):.3f} ± {training_results.get('cv_std', 0):.3f}")

                # Show classification report
                if 'classification_report' in training_results:
                    print(f"\n📊 Classification Performance:")
                    report = training_results['classification_report']
                    for class_name, metrics in report.items():
                        if isinstance(metrics, dict) and 'precision' in metrics:
                            print(f"  {class_name}: precision={metrics['precision']:.3f}, "
                                  f"recall={metrics['recall']:.3f}, f1={metrics['f1-score']:.3f}")

            # Show comparison results if available
            if 'comparison_results' in results and results['comparison_results']:
                print(f"\n🏆 Model Comparison:")
                for model_name, model_results in results['comparison_results'].items():
                    cv_mean = model_results.get('cv_mean', 0)
                    cv_std = model_results.get('cv_std', 0)
                    print(f"  {model_name}: {cv_mean:.3f} ± {cv_std:.3f}")

        print(f"\n📁 Generated Files:")
        print(f"  ✓ participants_at_risk_groups.csv - Processed participant data")
        print(f"  ✓ participant_embeddings.npz - Audio embeddings")
        print(f"  ✓ trained_model.joblib - Trained classifier")
        print(f"  ✓ audio/ - {total_files} mock audio files")

        return True

    except Exception as e:
        print(f"\n❌ Pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def cleanup_demo_files():
    """Clean up demo files."""
    print("\nCleaning up demo files...")

    # Remove audio files
    audio_dir = Path("audio")
    if audio_dir.exists():
        for audio_file in audio_dir.glob("*.wav"):
            audio_file.unlink()
        if not any(audio_dir.iterdir()):
            audio_dir.rmdir()
        print("✓ Removed mock audio files")

    # Remove embeddings and model files
    files_to_remove = [
        "participant_embeddings.npz",
        "trained_model.joblib"
    ]

    for filename in files_to_remove:
        filepath = Path(filename)
        if filepath.exists():
            filepath.unlink()
            print(f"✓ Removed {filename}")


def main():
    """Main demo function."""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup":
        cleanup_demo_files()
        return

    print("This demo will:")
    print("1. Create mock audio files for 10 participants")
    print("2. Run the complete audio classification pipeline")
    print("3. Demonstrate GPU acceleration and checkpointing")
    print("4. Train and evaluate ML models")
    print("5. Show performance metrics and comparisons")
    print()

    response = input("Continue with demo? (y/n): ")
    if response.lower() != 'y':
        print("Demo cancelled.")
        return

    success = run_demo_pipeline()

    if success:
        print(f"\n🎉 Demo completed successfully!")
        print(f"\nThe pipeline demonstrated:")
        print(f"✅ GPU acceleration (Apple Silicon MPS)")
        print(f"✅ Wav2Vec2 embedding extraction")
        print(f"✅ Per-participant aggregation")
        print(f"✅ XGBoost classification")
        print(f"✅ Model comparison and evaluation")
        print(f"✅ Checkpointing and resumption")

        print(f"\nTo clean up demo files, run:")
        print(f"python3 demo_pipeline.py --cleanup")
    else:
        print(f"\n❌ Demo failed. Check the error messages above.")


if __name__ == "__main__":
    main()
