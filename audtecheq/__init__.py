
from ._version import get_versions
__version__ = get_versions()['version']
del get_versions

try:
    from ._version import __version__
except ImportError:
    __version__ = "0+unknown"

# <PERSON><PERSON><PERSON> controlling the default globbing technique when using check_niimg
# and the os.path.expanduser usage in CacheMixin.
# Default value it True, set it to False to completely deactivate this
# behavior.
EXPAND_PATH_WILDCARDS = True

# list all submodules available in nilearn and version
__all__ = [
    "__version__",
    "utils",
    "conversion",
    "data"
]
