# These are required for developing the package (running the tests, building
# the documentation) but not necessarily required for _using_ it.
codecov
coverage
flake8
pytest
sphinx
duecredit
doctr
ipython
matplotlib
importlib_resources
pybids
librosa
pytorch
tensforflow
keras
pandas
# These are dependencies of various sphinx extensions for documentation.
numpydoc
sphinx-copybutton
sphinx_rtd_theme
sphinx-argparse
sphinx_material
sphinx_gallery
sphinx_design
sphinx-jsonschema
sphinx-tabs