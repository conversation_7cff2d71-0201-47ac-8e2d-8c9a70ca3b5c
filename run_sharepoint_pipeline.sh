#!/bin/bash
#SBATCH --job-name=audio_pipeline
#SBATCH --output=audio_pipeline_out.log
#SBATCH --error=audio_pipeline_err.log
#SBATCH --account=p32576
#SBATCH --partition=short
#SBATCH --nodes=1
#SBATCH --ntasks=64
#SBATCH --mem=220G
#SBATCH --time=04:00:00
#SBATCH --mail-type=END,FAIL
#SBATCH --mail-user=<EMAIL>

echo "Job started at: $(date)"

# Use your scratch directory where you have write permissions
SCRATCH_DIR="/scratch/vsx2019"
WORK_DIR="$SCRATCH_DIR/audtecheq_work"
mkdir -p "$WORK_DIR"

# Create a base directory for all data
BASE_DIR="$WORK_DIR/audtecheq_data"
mkdir -p "$BASE_DIR"

# Find the actual location of the script file
SCRIPT_PATH=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_PATH")
echo "Script directory: $SCRIPT_DIR"

# Load conda
# Find conda and activate it
# Try different possible conda locations
if [ -f "$SCRATCH_DIR/miniconda3/bin/conda" ]; then
    CONDA_PATH="$SCRATCH_DIR/miniconda3/bin/conda"
elif [ -f "$HOME/miniconda3/bin/conda" ]; then
    CONDA_PATH="$HOME/miniconda3/bin/conda"
else
    # Use which to find conda
    CONDA_PATH=$(which conda)
    if [ -z "$CONDA_PATH" ]; then
        echo "Error: Could not find conda. Please specify the correct path."
        exit 1
    fi
fi

echo "Using conda at: $CONDA_PATH"
eval "$($CONDA_PATH 'shell.bash' 'hook')"

# Create and activate a dedicated environment
ENV_NAME="audtecheq_env"
ENV_PATH="$SCRATCH_DIR/.conda/envs/$ENV_NAME"  # Use scratch directory for environments

# Check if environment exists, create if it doesn't
if [ ! -d "$ENV_PATH" ]; then
    echo "Creating new conda environment: $ENV_NAME"
    conda create -y -p $ENV_PATH python=3.11  # Use -p for custom location
else
    echo "Using existing environment: $ENV_NAME"
fi

# Activate the environment
conda activate $ENV_PATH

# Install required packages
echo "Installing required packages..."
pip install numpy pandas scikit-learn librosa torchaudio torch xgboost transformers
pip install Office365-REST-Python-Client>=2.3.0
pip install joblib pathlib2 psutil soundfile resampy matplotlib seaborn tqdm

# Set environment variables
export LD_LIBRARY_PATH=$CONDA_PREFIX/lib:$LD_LIBRARY_PATH

# Create directories for checkpoints and cache
CHECKPOINT_DIR="$BASE_DIR/checkpoints"
CACHE_DIR="$BASE_DIR/cache"
mkdir -p "$CHECKPOINT_DIR"
mkdir -p "$CACHE_DIR"

# List files in script directory to verify
echo "Files in script directory:"
ls -la "$SCRIPT_DIR"

# Run the SharePoint pipeline using the script in the same directory
echo "Starting SharePoint audio classification pipeline..."
python "$SCRIPT_DIR/sharepoint_pipeline.py" \
  --sharepoint-url "https://nuwildcat.sharepoint.com/teams/SOC-PedzSTARAdministration/" \
  --sharepoint-username "<EMAIL>" \
  --sharepoint-password "Hyelaw*100812" \
  --base-dir "$BASE_DIR" \
  --checkpoint-dir "$CHECKPOINT_DIR" \
  --cache-dir "$CACHE_DIR" \
  --max-participants 50 \
  --log-level INFO

echo "Job finished at: $(date)"
